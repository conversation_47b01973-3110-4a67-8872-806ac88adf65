# CSF TZ - Custom Fields Documentation

## Overview

This document provides comprehensive information about all custom fields in the CSF TZ application. Custom fields extend the functionality of existing DocTypes by adding business-specific data fields without modifying core ERPNext code.

## Custom Fields Structure

### Implementation Methods
CSF TZ implements custom fields through multiple approaches:
1. **JSON Configuration Files:** Structured field definitions in JSON format
2. **Python Patch Files:** Programmatic field creation with business logic
3. **Specialized Patches:** Feature-specific custom field implementations

### Custom Fields Directory Structure
```
apps/csf_tz/csf_tz/patches/custom_fields/
├── custom_fields_json/           # JSON-based field definitions
├── *.py                         # Python-based field creation patches
└── __pycache__/                 # Compiled Python files
```

## JSON-Based Custom Fields

### 1. Employee ISO20022 Banking Fields
**File:** `01_init.json`
**Target DocType:** Employee

| Field Name | Field Type | Purpose | Position |
|------------|------------|---------|----------|
| iso20022_details | Tab Break | Creates ISO20022 banking tab | After worker_subsistence |
| custom_employee_country_code | Link (Country) | Employee's country for banking | After iso20022_details |
| custom_beneficiary_bank_bic | Data | Bank Identifier Code | After custom_employee_country_code |
| custom_bank_country_code | Data | Bank's country code | After custom_beneficiary_bank_bic |
| custom_bank_account_name | Data | Account holder name | After custom_bank_country_code |
| custom_employee_country | Data | Employee's country name | After custom_bank_account_name |

**Business Purpose:** Enables international banking compliance and ISO20022 payment processing for employee salary payments.

### 2. Additional Cost Management
**File:** `02_additional_costs.json`
**Purpose:** Adds fields for managing additional costs in various business processes.

### 3. Bank and Supplier Integration
**File:** `02_bank_supplier.json`
**Purpose:** Enhances bank and supplier management with additional data fields.

### 4. Landed Cost Item Enhancements
**File:** `03_landed_cost_item.json`
**Purpose:** Extends landed cost functionality with additional item-specific fields.

### 5. OTP Authentication Fields
**File:** `04_authotp_validated.json`
**Purpose:** Adds OTP validation fields for enhanced security.

### 6. Material Request Management
**File:** `05_close_material_request.json`
**Purpose:** Adds fields for material request closure and tracking.

### 7. Salary Component Flexibility
**File:** `06_allow_negative_salary_component.json`
**Purpose:** Enables negative salary components for deductions and adjustments.

### 8. Travel Request System
**File:** `06_travel_request.json`
**Purpose:** Adds travel request management fields.

### 9. Bank Charges in Payment Entry
**File:** `07_bank_charges_payment_entry.json`
**Purpose:** Tracks bank charges in payment transactions.

### 10. Employee Management Enhancements
**File:** `07_employee_custom_fields.json`
**Purpose:** Additional employee data fields for comprehensive HR management.

### 11. Employee QR Code Generation
**File:** `07_employee_generate_contact_qr.json`
**Purpose:** Enables QR code generation for employee contact information.

### 12. Supplier Information in Landed Cost
**File:** `09_supplier_name_in_landed_cost_voucger.json`
**Purpose:** Links supplier information to landed cost vouchers.

### 13. Sales Order Integration
**File:** `10_unallocated_get_so.json`
**Purpose:** Manages unallocated amounts with sales order references.

### 14. Payment Entry Restrictions
**File:** `11_restrict_unallocated_amount_for_supplier_payment_entry.json`
**Purpose:** Controls unallocated amounts in supplier payment entries.

## Python-Based Custom Fields

### 1. Stock Entry Enhancements
**File:** `add_fields_in_stock_entry.py`
**Purpose:** Adds specialized fields to Stock Entry DocType for inventory management.

### 2. Attendance and Overtime Calculation
**File:** `attendance_overtime_calculation_custom_fields.py`
**Purpose:** Extends attendance tracking with overtime calculation capabilities.

**Key Features:**
- Overtime hour tracking
- Automatic overtime calculation
- Integration with payroll processing

### 3. OTP Authentication System
**File:** `auth_otp_custom_fields.py`
**Purpose:** Implements comprehensive OTP authentication fields across multiple DocTypes.

**Security Features:**
- OTP validation tracking
- Authentication status fields
- Security audit trails

### 4. Additional Salary Management
**File:** `create_custom_fields_for_additional_salary.py`
**Purpose:** Creates fields for managing additional salary components.

**Functionality:**
- Bonus tracking
- Incentive management
- Special allowances

### 5. Trade-In Feature Implementation
**File:** `create_custom_fields_for_trade_in_feature.py`
**Purpose:** Enables trade-in functionality for asset and inventory management.

**Business Logic:**
- Trade-in value calculation
- Asset exchange tracking
- Depreciation adjustments

### 6. Educational Fields Migration
**File:** `custom_fields_for_removed_edu_fields_in_csf_tz.py`
**Purpose:** Recreates educational fields that were removed from standard ERPNext.

**Educational Data:**
- Academic qualifications
- Training records
- Certification tracking

### 7. Employee Field Cleanup
**File:** `delete_employee_custom_fields.py`
**Purpose:** Removes obsolete employee custom fields during system upgrades.

### 8. Payroll Approval Workflow
**File:** `payroll_approval_custom_fields.py`
**Purpose:** Implements payroll approval workflow fields.

**Approval Features:**
- Multi-level approval tracking
- Approval status management
- Audit trail maintenance

### 9. Payroll Cost Center Integration
**File:** `payroll_cost_center_custom_fields.py`
**Purpose:** Links payroll processing with cost center management.

**Cost Management:**
- Department-wise payroll allocation
- Cost center tracking
- Budget integration

## Custom Fields by Business Function

### Human Resources Management

#### Employee Banking and Payments
- **ISO20022 Compliance:** International banking standard fields
- **Bank Account Details:** Comprehensive banking information
- **Payment Processing:** Automated salary payment fields

#### Payroll and Attendance
- **Overtime Calculation:** Automated overtime tracking
- **Additional Salary:** Bonus and incentive management
- **Approval Workflow:** Multi-level payroll approval
- **Cost Center Integration:** Department-wise payroll allocation

#### Employee Information
- **Contact Management:** QR code generation for contact sharing
- **Educational Records:** Academic and training information
- **Authentication:** OTP-based security fields

### Financial Management

#### Payment Processing
- **Bank Charges:** Automatic bank charge calculation
- **Unallocated Amounts:** Payment allocation management
- **Supplier Payments:** Enhanced supplier payment tracking

#### Cost Management
- **Additional Costs:** Comprehensive cost tracking
- **Landed Costs:** Import and shipping cost management
- **Cost Center Allocation:** Department-wise cost distribution

### Inventory and Operations

#### Stock Management
- **Stock Entry Enhancements:** Advanced inventory tracking
- **Material Requests:** Request closure and tracking
- **Trade-In Processing:** Asset exchange management

#### Travel and Logistics
- **Travel Requests:** Employee travel management
- **Vehicle Integration:** Fleet management fields

### Security and Authentication

#### OTP System
- **Multi-Factor Authentication:** OTP validation across DocTypes
- **Security Tracking:** Authentication audit trails
- **Access Control:** Enhanced security measures

## Technical Implementation Details

### Field Creation Pattern
```python
def create_custom_fields(fields_dict):
    for doctype, fields in fields_dict.items():
        for field in fields:
            if not frappe.db.exists("Custom Field", f"{doctype}-{field['fieldname']}"):
                custom_field = frappe.get_doc({
                    "doctype": "Custom Field",
                    "dt": doctype,
                    **field
                })
                custom_field.insert()
```

### JSON Field Structure
```json
{
    "dt": "DocType_Name",
    "fieldname": "custom_field_name",
    "label": "Field Label",
    "fieldtype": "Field_Type",
    "insert_after": "existing_field",
    "options": "Link_DocType_or_Options",
    "fetch_from": "linked_field.property",
    "reqd": 0,
    "hidden": 0,
    "read_only": 0
}
```

### Field Properties Used
- **fetch_from:** Automatically fetches values from linked documents
- **fetch_if_empty:** Only fetches if field is empty
- **translatable:** Enables field translation
- **no_copy:** Prevents copying when duplicating documents
- **allow_on_submit:** Allows modification after document submission

## Business Impact Analysis

### Operational Efficiency
- **Automated Data Entry:** Fetch properties reduce manual input
- **Streamlined Workflows:** Custom fields support business processes
- **Enhanced Tracking:** Comprehensive data capture for reporting

### Compliance and Reporting
- **Banking Compliance:** ISO20022 fields ensure international payment compliance
- **Audit Trails:** Security and approval fields provide audit capabilities
- **Financial Reporting:** Cost center and additional cost fields improve reporting

### User Experience
- **Organized Information:** Tab breaks and sections organize complex forms
- **Contextual Data:** Fields positioned logically within existing workflows
- **Flexible Configuration:** Optional fields accommodate varying business needs

## Maintenance Guidelines

### Adding New Custom Fields
1. **Choose Implementation Method:** JSON for simple fields, Python for complex logic
2. **Follow Naming Convention:** Use "custom_" prefix for all custom fields
3. **Position Appropriately:** Use insert_after to place fields logically
4. **Test Thoroughly:** Verify field behavior in all relevant scenarios

### Field Modification Best Practices
1. **Backup Data:** Always backup before modifying existing custom fields
2. **Test in Development:** Verify changes don't break existing functionality
3. **Update Documentation:** Document all field changes and their purposes
4. **Consider Dependencies:** Check if other customizations depend on the field

### Troubleshooting Common Issues
1. **Field Not Appearing:** Check field permissions and hidden property
2. **Fetch Not Working:** Verify fetch_from path and linked document existence
3. **Validation Errors:** Review field requirements and data types
4. **Performance Issues:** Consider indexing for frequently queried fields

## Related Documentation
- [CSF TZ Property Setters Documentation](CSF_TZ_Property_Setters_Documentation.md)
- [CSF TZ Patch Files Documentation](CSF_TZ_Patch_Files_Documentation.md)
- [CSF TZ DocTypes Documentation](CSF_TZ_DocTypes_Documentation.md)