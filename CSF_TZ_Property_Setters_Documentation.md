# CSF TZ - Property Setters Documentation

## Overview

This document provides comprehensive information about property setters in the CSF TZ application. Property setters are used to modify the behavior and appearance of standard DocType fields without altering the core system files.

## Property Setters Implementation

### Main Property Setter File
**Location:** `apps/csf_tz/csf_tz/patches/property_setter/property_setter.py`

The CSF TZ application implements **extensive property setters** with over 100 individual property modifications across multiple DocTypes.

## Property Setters by DocType

### 1. Supplier DocType Modifications

| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| tax_id | label | "TIN" | Changes label from "Tax ID" to "TIN" for local compliance |
| tax_id | bold | 1 | Makes TIN field bold for emphasis |
| - | read_only_onload | "" | Removes read-only restriction |

**Business Impact:** Adapts supplier forms for Tanzanian tax requirements where TIN (Tax Identification Number) is the standard term.

### 2. Sales Invoice Enhancements

#### Main DocType Properties
| Property | Value | Purpose |
|----------|-------|---------|
| search_fields | "posting_date, due_date, customer, base_grand_total, outstanding_amount" | Improves search functionality |

#### Sales Invoice Item Properties
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| cost_center | in_list_view | 1 | Shows cost center in list view |
| cost_center | columns | 1 | Sets column width |
| item_code | columns | 3 | Wider column for item codes |
| qty | columns | 1 | Narrow column for quantity |
| warehouse | in_list_view | 0 | Hides warehouse from list view |
| batch_no | in_list_view | 0 | Hides batch number from list view |
| item_tax_template | fetch_if_empty | 1 | Auto-fetches tax template |
| item_tax_template | default | "" | Clears default tax template |
| discount_percentage | precision | 6 | Sets 6 decimal places for discounts |

#### Letter Head Integration
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| letter_head | fetch_from | "company.default_letter_head" | Auto-fetches company letterhead |
| letter_head | fetch_if_empty | 1 | Only fetches if field is empty |

#### POS and Healthcare Integration
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| is_pos | in_standard_filter | 1 | Adds POS filter to list view |
| pos_profile | in_standard_filter | 1 | Adds POS profile filter |
| posting_date | in_list_view | 1 | Shows posting date in list |
| loyalty_points_redemption | depends_on | "eval: !in_list(frappe.user_roles, \"Healthcare Receptionist\")" | Hides loyalty points for healthcare receptionists |

### 3. Vehicle Management Customizations

#### Vehicle Log Enhancements
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| license_plate | in_standard_filter | 1 | Enables filtering by license plate |
| employee | fetch_from | "license_plate.driver" | Auto-fetches driver from vehicle |
| employee | fetch_if_empty | 1 | Only fetches if empty |
| date | in_list_view | 1 | Shows date in list view |
| date | default | "Today" | Sets today as default date |
| odometer | in_list_view | 1 | Shows odometer reading in list |
| - | quick_entry | 0 | Disables quick entry for vehicle logs |

#### Vehicle Service Customizations
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| service_item | options | "NOT SELECTED\nBrake Oil\nBrake Pad\nClutch Plate\nEngine Oil\nOil Change\nWheels" | Predefined service items |
| service_item | in_list_view | 0 | Hides from list view |
| type | options | "Repair\nInspection\nService\nChange" | Service type options |
| frequency | in_list_view | 0 | Hides frequency from list view |

### 4. Financial Management Modifications

#### Journal Entry Improvements
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| accounts | allow_bulk_edit | 1 | Enables bulk editing of accounts |
| total_debit | in_list_view | 1 | Shows total debit in list |
| total_amount | in_list_view | 0 | Hides total amount from list |

#### Journal Entry Account Customizations
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| accounting_dimensions_section | collapsible | 0 | Makes section non-collapsible |
| reference_name | in_list_view | 0 | Hides reference name |
| account | width | "" | Removes fixed width |
| party | columns | 2 | Sets party column width |
| party_type | columns | 1 | Sets party type column width |

#### Payment Entry Enhancements
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| letter_head | fetch_from | "company.default_letter_head" | Auto-fetches letterhead |
| payment_accounts_section | collapsible | 0 | Non-collapsible payment section |
| section_break_12 | collapsible | 0 | Non-collapsible section |

#### Payment Entry Reference Optimizations
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| due_date | width | "23" | Sets specific width |
| due_date | columns | 1 | Single column width |
| reference_doctype | columns | 0 | Hides doctype column |
| reference_doctype | in_list_view | 0 | Removes from list view |
| reference_name | columns | 3 | Wider reference name column |

### 5. Inventory and Purchasing

#### Purchase Invoice Item
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| cost_center | default | "" | Clears default cost center |
| discount_percentage | precision | 6 | 6 decimal places for discounts |

#### Item Price Management
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| brand | in_list_view | 0 | Hides brand from list |
| price_list | in_list_view | 1 | Shows price list in list |
| valid_from | in_list_view | 1 | Shows validity date |

#### Stock Entry Automation
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| from_warehouse | fetch_from | "repack_template.default_warehouse" | Auto-fetches warehouse from template |

### 6. Customer and Account Management

#### Customer Enhancements
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| tax_id | label | "TIN" | Changes to TIN for local compliance |
| - | search_fields | "customer_name,customer_group,territory, mobile_no" | Enhanced search capabilities |

#### Account Improvements
| Property | Value | Purpose |
|----------|-------|---------|
| search_fields | "account_number, root_type, account_type" | Better account search |

### 7. System Performance Optimizations

#### Change Tracking Disabled
The following DocTypes have change tracking disabled for performance:

| DocType | Purpose |
|---------|---------|
| Energy Point Log | Reduces logging overhead |
| Route History | Improves navigation performance |
| Notification Log | Reduces notification tracking |
| Scheduled Job Log | Minimizes job logging |
| Healthcare Service Order | Healthcare performance optimization |
| Healthcare Insurance Claim | Healthcare performance optimization |
| Comment | Reduces comment tracking |
| Workflow Action | Improves workflow performance |

### 8. Specialized Business Logic

#### Budget Configuration
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| budget_against | options | Multiple healthcare and business options | Extensive budget categorization |

#### Payment Schedule
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| payment_amount | options | "Company:company:default_currency" | Currency formatting |

#### Bank Reconciliation
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| payment_entry | columns | 1 | Optimized column width |
| posting_date | columns | 1 | Optimized date column |
| posting_date | in_list_view | 1 | Shows posting date |

#### Payment Reconciliation
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| posting_date | columns | 1 | Optimized column width |
| posting_date | in_list_view | 1 | Shows posting date |

### 9. CSF TZ Specific Customizations

#### Piecework Management
| DocType | Property | Value | Purpose |
|---------|----------|-------|---------|
| Piecework Type | search_fields | "task_name" | Enhanced piecework search |

#### Document Management
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| attachment | in_list_view | 1 | Shows attachments in Document Attachment list |

#### Operations
| Property | Value | Purpose |
|----------|-------|---------|
| image_field | "image" | Sets image field for operations |

#### Delivery Note
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| items | allow_bulk_edit | 0 | Disables bulk editing for delivery note items |

#### Report Customization
| Field Name | Property | Value | Purpose |
|------------|----------|-------|---------|
| javascript | depends_on | "" | Removes JavaScript dependency |

## Property Setter Categories Summary

### User Interface Improvements
- **List View Optimization:** 25+ fields modified for better list display
- **Column Width Management:** Optimized spacing for better readability
- **Search Enhancement:** Improved search fields for key DocTypes

### Business Process Adaptation
- **Local Compliance:** TIN labeling for Tanzanian tax requirements
- **Vehicle Management:** Comprehensive fleet management customizations
- **Healthcare Integration:** Specialized healthcare workflow modifications

### Performance Optimizations
- **Change Tracking:** Disabled for 8 high-volume DocTypes
- **Bulk Operations:** Enabled/disabled based on business needs
- **Auto-fetch Logic:** Intelligent field population

### Financial Management
- **Precision Control:** 6 decimal places for discount calculations
- **Currency Formatting:** Proper currency display
- **Account Management:** Enhanced search and display

## Technical Implementation

### Property Setter Pattern
```python
{
    "doctype": "DocType_Name",
    "field_name": "field_name",  # Optional for DocType-level properties
    "property": "property_name",
    "property_type": "Data|Check|Int|Text|Select",
    "value": "property_value"
}
```

### Execution Process
1. **Batch Processing:** All property setters applied in single transaction
2. **Error Handling:** Individual property setter failures don't stop execution
3. **Validation:** Field validation bypassed for system efficiency
4. **Commit:** Database commit ensures persistence

## Business Impact Analysis

### Operational Efficiency
- **Reduced Clicks:** Auto-fetch properties minimize manual data entry
- **Better Visibility:** List view optimizations improve data scanning
- **Faster Search:** Enhanced search fields speed up record location

### Compliance and Localization
- **Tax Compliance:** TIN labeling meets local regulatory requirements
- **Business Terminology:** Field labels adapted to local business language
- **Process Alignment:** Workflow modifications match business processes

### System Performance
- **Reduced Overhead:** Change tracking disabled for high-volume DocTypes
- **Optimized Display:** Column width optimization improves page load times
- **Efficient Processing:** Bulk edit controls prevent system overload

## Maintenance Guidelines

### Adding New Property Setters
1. **Test in Development:** Verify property changes don't break functionality
2. **Document Business Reason:** Clearly document why property change is needed
3. **Consider Impact:** Assess effect on existing data and user workflows
4. **Update Documentation:** Add new property setters to this documentation

### Troubleshooting Common Issues
1. **Property Not Applied:** Check property name spelling and DocType existence
2. **Field Not Found:** Verify field exists in target DocType
3. **Value Not Accepted:** Ensure property type matches expected value format
4. **Performance Issues:** Review if property changes affect query performance

## Related Documentation
- [CSF TZ Custom Fields Documentation](CSF_TZ_Custom_Fields_Documentation.md)
- [CSF TZ Patch Files Documentation](CSF_TZ_Patch_Files_Documentation.md)
- [CSF TZ DocTypes Documentation](CSF_TZ_DocTypes_Documentation.md)

## Technical References
- [Frappe Property Setter Documentation](https://frappeframework.com/docs/user/en/desk/customize/property-setter)
- [Property Types Reference](https://frappeframework.com/docs/user/en/python-api/property-setter)