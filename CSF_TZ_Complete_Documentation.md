# CSF TZ - Complete Application Documentation

## Table of Contents
1. [Application Overview](#application-overview)
2. [Architecture Summary](#architecture-summary)
3. [Component Documentation](#component-documentation)
4. [Business Processes](#business-processes)
5. [Technical Implementation](#technical-implementation)
6. [DocTypes Inventory](#doctypes-inventory)
7. [Independent Scripts and Modules](#independent-scripts-and-modules)
8. [Deployment and Maintenance](#deployment-and-maintenance)

## Application Overview

### What is CSF TZ?
CSF TZ (Country Specific Functionality Tanzania) is a comprehensive Frappe application that provides Tanzania-specific business functionality and compliance features for ERPNext. The application extends ERPNext with local business requirements, regulatory compliance, and operational enhancements tailored for Tanzanian businesses.

### Key Features
- **Comprehensive HR Management:** Advanced payroll, overtime, and employee management
- **Financial Compliance:** Withholding tax, bank integration, and local accounting standards
- **Inventory Management:** Enhanced stock management with trade-in functionality
- **Vehicle Fleet Management:** Complete fleet tracking and maintenance
- **Educational Institution Management:** Student management and fee processing
- **Banking Integration:** Stanbic Bank integration and automated reconciliation
- **Security Enhancements:** OTP authentication and advanced security features
- **Clearing and Forwarding:** Import/export documentation and customs management

### Business Value
- **Local Compliance:** Ensures adherence to Tanzanian business regulations
- **Operational Efficiency:** Streamlines complex business processes
- **Integration:** Seamless integration with local banks and service providers
- **Scalability:** Supports businesses from small enterprises to large corporations
- **Customization:** Extensive customization capabilities for specific business needs

## Architecture Summary

### Application Structure
```
csf_tz/
├── after_sales_services/      # Post-sale service management
├── ai_integration/           # AI and machine learning features
├── authotp/                 # OTP authentication system
├── clearing_and_forwarding/ # Import/export management
├── csf_tz/                  # Core application modules
├── feedback/                # Customer feedback system
├── fleet_management/        # Vehicle fleet management
├── meal_count/              # Employee meal tracking
├── purchase_and_stock_management/ # Inventory management
├── sales_and_marketing/     # Sales and marketing tools
├── stanbic/                 # Bank integration
└── workshop/                # Workshop and maintenance
```

### Data Flow Architecture
1. **User Interface Layer:** Enhanced forms with custom JavaScript
2. **Business Logic Layer:** Python hooks and custom APIs
3. **Data Layer:** Extended DocTypes with custom fields
4. **Integration Layer:** Bank APIs, OTP services, and external systems
5. **Compliance Layer:** Tax calculations, regulatory reporting

## Component Documentation

### 1. Patch Files
**Document:** [CSF TZ Patch Files Documentation](CSF_TZ_Patch_Files_Documentation.md)

**Summary:**
- **150+ Employee Advance Patches:** Comprehensive data migration for employee advance status updates
- **Custom Fields Creation:** Extensive custom field implementation across multiple DocTypes
- **Property Setter Application:** System-wide property modifications
- **Permission Cleanup:** Core DocType permission management

**Key Impact:**
- Ensures data integrity during system upgrades
- Implements business-specific field requirements
- Maintains compliance with local regulations

### 2. Property Setters
**Document:** [CSF TZ Property Setters Documentation](CSF_TZ_Property_Setters_Documentation.md)

**Summary:**
- **100+ Property Modifications:** Extensive customization of field behavior
- **UI/UX Enhancements:** Improved user interface and experience
- **Performance Optimizations:** Change tracking disabled for high-volume DocTypes
- **Local Compliance:** TIN labeling and Tanzanian business terminology

**Key Customizations:**
- Vehicle management form enhancements
- Financial document optimizations
- Healthcare workflow improvements
- Sales and purchase process streamlining

### 3. Custom Fields
**Document:** [CSF TZ Custom Fields Documentation](CSF_TZ_Custom_Fields_Documentation.md)

**Summary:**
- **Multi-Method Implementation:** JSON and Python-based field creation
- **Business Function Coverage:** HR, Finance, Inventory, Security, Education
- **ISO20022 Banking:** International banking compliance fields
- **OTP Security:** Multi-factor authentication fields

**Major Field Categories:**
- Employee banking and payment fields
- Trade-in functionality fields
- Additional salary management fields
- Travel and expense management fields

### 4. Hooks Configuration
**Document:** [CSF TZ Hooks Documentation](CSF_TZ_Hooks_Documentation.md)

**Summary:**
- **25+ DocType JavaScript Files:** Comprehensive client-side enhancements
- **Extensive Document Events:** Business logic integration across all major DocTypes
- **Scheduled Tasks:** Automated background processing
- **Method Overrides:** Enhanced core ERPNext functionality

**Key Integrations:**
- Sales invoice and delivery note automation
- Payroll and HR processing enhancements
- Bank reconciliation and payment processing
- Universal visibility system implementation

## Business Processes

### Human Resources Management

#### Payroll Processing
1. **Salary Slip Generation:** Custom calculation logic with overtime integration
2. **Additional Salary Management:** Automated bonus and incentive processing
3. **Employee Advance Processing:** Automatic payment and expense entries
4. **Payroll Approval Workflow:** Multi-level approval system

#### Attendance and Time Management
1. **Overtime Calculation:** Automated overtime tracking and calculation
2. **Employee Check-in Validation:** Enhanced time tracking
3. **Meal Count Management:** Employee meal tracking and billing

### Financial Management

#### Payment Processing
1. **Bank Charges Integration:** Automatic bank charge calculation and posting
2. **Withholding Tax Management:** Automated tax calculations for sales and purchases
3. **Payment Entry Validation:** Enhanced payment processing with security features
4. **Bank Reconciliation:** Daily automated reconciliation with Stanbic Bank

#### Accounting and Compliance
1. **Chart of Accounts Enhancement:** Local account structure and reporting
2. **Tax Compliance:** Automated tax calculations and reporting
3. **Financial Reporting:** Enhanced reports for local compliance
4. **Currency Management:** Multi-currency support with local regulations

### Inventory and Operations

#### Stock Management
1. **Enhanced Stock Entry:** Weight calculations and BOM integration
2. **Trade-in Processing:** Asset exchange and depreciation management
3. **Material Request Automation:** Auto-closure and status management
4. **Stock Reconciliation:** Weekly automated reconciliation

#### Sales and Delivery
1. **Automatic Delivery Note Creation:** Seamless sales to delivery workflow
2. **Trade-in Sales Integration:** Complex trade-in transaction processing
3. **Price Management:** Dynamic pricing with trade-in considerations
4. **Customer Management:** Enhanced customer data with OTP security

### Vehicle Fleet Management

#### Fleet Operations
1. **Vehicle Tracking:** Comprehensive vehicle information management
2. **Maintenance Scheduling:** Automated maintenance reminders and tracking
3. **Fuel Management:** Fuel consumption tracking and reporting
4. **Driver Management:** Driver assignment and performance tracking

#### Compliance and Monitoring
1. **Insurance Management:** Insurance policy tracking and renewals
2. **Fine Management:** Traffic fine tracking and payment
3. **Parking Bill Management:** Automated parking fee tracking
4. **Vehicle Inspection:** Regular inspection scheduling and tracking

### Educational Institution Management

#### Student Management
1. **Student Applicant Processing:** Application and admission workflow
2. **Program Enrollment:** Course enrollment and management
3. **Fee Management:** Student fee calculation and collection
4. **Academic Records:** Comprehensive academic tracking

#### Financial Management
1. **Fee Collection:** Automated fee collection with bank integration
2. **Scholarship Management:** Scholarship tracking and disbursement
3. **Financial Reporting:** Educational institution financial reports
4. **Budget Management:** Academic budget planning and tracking

## Technical Implementation

### Framework Integration
- **Frappe Framework:** Built on Frappe v14+ framework
- **ERPNext Integration:** Extends ERPNext with Tanzania-specific functionality
- **HRMS Integration:** Enhanced human resource management
- **Database:** Uses MariaDB for data storage with extensive customizations

### Security Implementation
- **OTP Authentication:** Multi-factor authentication across critical processes
- **Role-Based Access:** Enhanced permission management
- **Audit Trails:** Comprehensive change tracking and logging
- **Data Encryption:** Secure storage of sensitive information

### Performance Optimization
- **Scheduled Tasks:** Background processing for heavy operations
- **Caching Strategies:** Optimized data retrieval and storage
- **Database Optimization:** Indexed fields and optimized queries
- **Resource Management:** Efficient memory and CPU usage

### Integration Capabilities
- **Banking APIs:** Direct integration with Stanbic Bank
- **SMS Services:** OTP and notification services
- **Government Systems:** Tax authority and regulatory compliance
- **Third-party Services:** Various business service integrations

## DocTypes Inventory

### Total DocTypes: 200+

The CSF TZ application includes over 200 custom DocTypes organized across 11 major modules:

#### 1. After Sales Services (12 DocTypes)
- **Machine Strip Request:** Equipment maintenance requests
- **Pre Delivery Inspection:** Quality control processes
- **Maintenance Schedule:** Service scheduling
- **Requested Payments:** Payment request management

#### 2. AI Integration (2 DocTypes)
- **LLM Settings:** AI model configuration
- **OpenAI Query Log:** AI interaction tracking

#### 3. Auth OTP (2 DocTypes)
- **AuthOTP Settings:** OTP configuration
- **OTP Register:** OTP tracking and validation

#### 4. Clearing and Forwarding (25 DocTypes)
- **Import/Export Management:** Complete import/export workflow
- **Container Management:** Container tracking and processing
- **Border Processing:** Customs and border procedures
- **Bond Management:** Customs bond handling

#### 5. Core CSF TZ (85 DocTypes)
- **Financial Management:** Bank charges, payment reconciliation
- **HR Management:** Piecework, salary components, employee management
- **Inventory Management:** Stock transfers, delivery exchanges
- **System Management:** SQL processes, visibility controls
- **Insurance Management:** Vehicle insurance and cover notes
- **Geographic Data:** Tanzania regions, districts, villages, wards

#### 6. Feedback System (4 DocTypes)
- **Feedback Forms:** Customer feedback collection
- **Feedback Templates:** Standardized feedback formats
- **Feedback Responses:** Response tracking and analysis

#### 7. Fleet Management (45 DocTypes)
- **Vehicle Management:** Comprehensive vehicle tracking
- **Maintenance Systems:** Service scheduling and tracking
- **Inspection Checklists:** Detailed vehicle inspections
- **Route Management:** Trip planning and tracking
- **Equipment Management:** Fleet equipment tracking

#### 8. Meal Count (5 DocTypes)
- **Biometric Integration:** Employee meal tracking
- **Meal Type Management:** Meal category configuration
- **Device Management:** Biometric device configuration

#### 9. Purchase and Stock Management (6 DocTypes)
- **Bin Management:** Warehouse bin organization
- **Order Tracking:** Purchase order monitoring
- **Item Management:** Enhanced item tracking

#### 10. Sales and Marketing (8 DocTypes)
- **Customer Management:** Enhanced customer tracking
- **Sales Analytics:** Past sales analysis
- **Marketing Tools:** Communication and alerts
- **Payment Planning:** Customer payment schedules

#### 11. Stanbic Bank Integration (3 DocTypes)
- **Payment Processing:** Bank payment integration
- **Settings Management:** Bank configuration
- **Transaction Tracking:** Payment monitoring

#### 12. Workshop Management (8 DocTypes)
- **Service Management:** Workshop service tracking
- **Item Management:** Workshop inventory
- **Request Processing:** Service request workflow

### Key DocType Categories

#### Master Data DocTypes (30+)
- Geographic data (Regions, Districts, Villages, Wards)
- Vehicle types and equipment
- Service types and categories
- Customer and supplier enhancements

#### Transaction DocTypes (100+)
- Sales and purchase transactions
- HR and payroll transactions
- Inventory movements
- Service requests and maintenance

#### Configuration DocTypes (40+)
- System settings and configurations
- User preferences and visibility
- Integration settings
- Workflow configurations

#### Reporting DocTypes (30+)
- Financial reports and analytics
- HR reports and dashboards
- Inventory reports
- Compliance reports

## Independent Scripts and Modules

### Core Utility Modules

#### 1. Custom API Module (`custom_api.py`)
**Location:** `apps/csf_tz/csf_tz/custom_api.py`
**Purpose:** Central API module containing business logic functions

**Key Functions:**
- **Sales and Delivery Automation:** Automatic delivery note creation
- **Trade-in Processing:** Complex trade-in transaction handling
- **Tax Calculations:** Withholding tax and VAT processing
- **Stock Management:** Inventory calculations and validations
- **QR Code Generation:** Document QR code creation

#### 2. Banking Integration Module (`bank_api.py`)
**Location:** `apps/csf_tz/csf_tz/bank_api.py`
**Purpose:** Banking system integration and reconciliation

**Key Functions:**
- **Payment Processing:** Bank payment integration
- **Reconciliation:** Automated bank reconciliation
- **Fee Collection:** Educational fee collection via banking
- **Callback Handling:** Bank callback processing

#### 3. CSF TZ Hooks Module (`csftz_hooks/`)
**Location:** `apps/csf_tz/csf_tz/csftz_hooks/`
**Purpose:** Business logic hooks for various DocTypes

**Sub-modules:**
- **Payroll Processing:** Salary slip and payroll entry logic
- **Stock Management:** Stock entry and material request processing
- **Employee Management:** Employee advance and additional salary
- **Purchase Management:** Purchase order and material request
- **Banking:** Bank charges and payment entry processing

#### 4. Stanbic Bank Integration (`stanbic/`)
**Location:** `apps/csf_tz/csf_tz/stanbic/`
**Purpose:** Stanbic Bank specific integration

**Key Features:**
- **SFTP Integration:** Automated file synchronization
- **Payment Processing:** Bank payment initiation
- **File Processing:** Bank statement processing
- **Reconciliation:** Automated bank reconciliation

#### 5. Authentication Module (`authotp/`)
**Location:** `apps/csf_tz/csf_tz/authotp/`
**Purpose:** OTP authentication system

**Key Features:**
- **OTP Generation:** Secure OTP creation
- **Validation:** OTP verification processes
- **Integration:** OTP integration across DocTypes
- **Security:** Enhanced security measures

### Specialized Business Modules

#### 1. Fleet Management System
**Location:** `apps/csf_tz/csf_tz/fleet_management/`
**Purpose:** Comprehensive vehicle fleet management

**Features:**
- **Vehicle Tracking:** Real-time vehicle monitoring
- **Maintenance Management:** Service scheduling and tracking
- **Fuel Management:** Fuel consumption tracking
- **Route Optimization:** Trip planning and optimization

#### 2. Clearing and Forwarding System
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/`
**Purpose:** Import/export and customs management

**Features:**
- **Documentation Management:** Import/export documentation
- **Container Tracking:** Container movement tracking
- **Customs Processing:** Border and customs procedures
- **Bond Management:** Customs bond handling

#### 3. Educational Management System
**Location:** Integrated across multiple modules
**Purpose:** Educational institution management

**Features:**
- **Student Management:** Student lifecycle management
- **Fee Management:** Fee calculation and collection
- **Academic Tracking:** Academic progress monitoring
- **Financial Management:** Educational finance management

### Utility and Helper Modules

#### 1. Visibility System (`visibility.py`)
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/visibility/`
**Purpose:** Universal field visibility control

**Features:**
- **Dynamic Visibility:** Conditional field visibility
- **Rule Engine:** Configurable visibility rules
- **Performance Optimization:** Efficient visibility processing

#### 2. Report Extensions (`reports/`)
**Location:** Various report directories
**Purpose:** Enhanced reporting capabilities

**Features:**
- **Custom Reports:** Business-specific reports
- **Data Analytics:** Advanced data analysis
- **Export Capabilities:** Multiple export formats
- **Interactive Reports:** User-friendly report interfaces

#### 3. Print Format Enhancements
**Location:** Various DocType directories
**Purpose:** Enhanced document printing

**Features:**
- **Custom Print Formats:** Business-specific document formats
- **QR Code Integration:** Document QR codes
- **Multi-language Support:** Local language printing
- **Compliance Formats:** Regulatory compliant documents

## Deployment and Maintenance

### Installation Requirements
- **Frappe Framework:** v14+ with HRMS
- **Python:** 3.8+ with required packages
- **Database:** MariaDB 10.3+ with proper configuration
- **Redis:** For caching and background jobs
- **Node.js:** For JavaScript bundling and processing

### Installation Process
1. **App Installation:**
   ```bash
   bench get-app csf_tz
   bench install-app csf_tz
   ```

2. **Post-Installation Setup:**
   - Custom fields automatically created
   - Property setters applied
   - Permissions configured
   - Scheduled tasks activated

3. **Configuration:**
   - Company-specific settings
   - Bank integration setup
   - OTP service configuration
   - User role assignments

### Maintenance Tasks

#### Daily Operations
- **Monitor Scheduled Tasks:** Verify background job execution
- **Check Bank Reconciliation:** Ensure daily reconciliation success
- **Review Error Logs:** Address any system errors
- **Validate Data Integrity:** Check critical business data

#### Weekly Operations
- **Performance Review:** Monitor system performance metrics
- **Backup Verification:** Ensure backup processes are working
- **User Training:** Address user questions and training needs
- **System Updates:** Apply minor updates and patches

#### Monthly Operations
- **Comprehensive Backup:** Full system backup and verification
- **Performance Optimization:** Database optimization and cleanup
- **Security Review:** Review access logs and security measures
- **Business Process Review:** Evaluate and optimize workflows

### Troubleshooting

#### Common Issues
1. **Scheduled Task Failures:** Check background job queue and logs
2. **Bank Integration Issues:** Verify API credentials and connectivity
3. **OTP Service Problems:** Check SMS service configuration
4. **Performance Issues:** Review database queries and indexing

#### Support Resources
- **Application Logs:** Comprehensive logging for troubleshooting
- **Error Tracking:** Detailed error reporting and tracking
- **Documentation:** Extensive documentation for all features
- **Community Support:** Active community and support channels

## Related Documentation Links
- [CSF TZ Patch Files Documentation](CSF_TZ_Patch_Files_Documentation.md)
- [CSF TZ Property Setters Documentation](CSF_TZ_Property_Setters_Documentation.md)
- [CSF TZ Custom Fields Documentation](CSF_TZ_Custom_Fields_Documentation.md)
- [CSF TZ Hooks Documentation](CSF_TZ_Hooks_Documentation.md)

## Conclusion

CSF TZ represents a comprehensive business management solution specifically designed for Tanzanian businesses. With over 200 DocTypes, extensive customizations, and deep integration capabilities, it provides a complete ERP solution that addresses local business requirements while maintaining international standards.

The application's modular architecture, extensive customization capabilities, and robust integration features make it suitable for businesses of all sizes, from small enterprises to large corporations. Its focus on local compliance, operational efficiency, and user experience ensures that businesses can operate effectively while meeting all regulatory requirements.