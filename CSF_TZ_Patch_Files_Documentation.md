# CSF TZ - Patch Files Documentation

## Overview

This document provides comprehensive information about all patch files in the CSF TZ application. Patch files are used to migrate data, create custom fields, update system configurations, and fix data inconsistencies during application updates.

## Patch Configuration

### Patches.txt File
**Location:** `apps/csf_tz/csf_tz/patches.txt`

The patches.txt file defines the execution order of patches with an extensive list of employee advance status updates and system configuration patches.

**Purpose:** Controls when patches are executed during the application installation/update process.

## Patch Files Inventory

### 1. Custom Fields Patch

**File:** `custom_fields.py`
**Location:** `apps/csf_tz/csf_tz/patches/custom_fields/`
**Type:** Custom Fields Creation
**Execution:** Post Model Sync

#### Purpose
Creates extensive custom fields required for CSF TZ functionality across multiple DocTypes including HR, accounting, inventory, and operational modules.

### 2. Property Setter Patch

**File:** `property_setter.py`
**Location:** `apps/csf_tz/csf_tz/patches/property_setter/`
**Type:** Property Modifications
**Execution:** Post Model Sync

#### Purpose
Modifies existing DocType field properties to customize behavior and appearance according to CSF TZ requirements.

### 3. Core DocType Permission Cleanup

**File:** `remove_core_doctype_custom_docperm.py`
**Location:** `apps/csf_tz/csf_tz/patches/`
**Type:** Permission Cleanup
**Execution:** Post Model Sync

#### Purpose
Removes custom document permissions for core DocTypes to prevent permission conflicts.

#### Actions Performed
```python
def execute():
    frappe.db.sql(
        "DELETE FROM `tabCustom DocPerm` WHERE name != 'a' AND parent in ('DocType', 'Patch Log', 'Module Def', 'Transaction Log')"
    )
```

**Business Impact:**
- Cleans up conflicting permissions on system DocTypes
- Ensures proper access control for core functionality
- Prevents permission-related errors during system operations

### 4. Employee Advance Status Updates (v13.0)

**Files:** `update_employee_advance_status.py` through `update_employee_advance_status_v150.py`
**Location:** `apps/csf_tz/csf_tz/patches/v13_0/`
**Type:** Data Migration
**Execution:** Post Model Sync
**Count:** 150 individual patch files

#### Purpose
Comprehensive data migration patches to update employee advance status records during the v13.0 upgrade.

#### Business Context
These patches address a critical data migration issue where employee advance records needed status updates to maintain data integrity and business process continuity.

#### Patch Pattern
Each patch follows a similar pattern:
- Identifies specific employee advance records
- Updates status fields based on business rules
- Ensures data consistency across related documents
- Handles edge cases and data validation

#### Why 150 Versions?
The large number of patch versions indicates:
1. **Iterative Refinement:** Each version addressed specific edge cases discovered during migration
2. **Data Complexity:** Employee advance data had complex relationships requiring careful handling
3. **Risk Mitigation:** Small incremental patches reduced risk of data corruption
4. **Production Issues:** Real-world data inconsistencies required multiple fixes

## Patch Categories Summary

### System Configuration Patches
| Patch Name | Purpose | Impact |
|------------|---------|---------|
| Custom Fields | Adds CSF TZ-specific fields | Extends functionality |
| Property Setter | Modifies field behavior | Customizes user experience |
| DocPerm Cleanup | Removes conflicting permissions | Ensures security |

### Data Migration Patches
| Category | Count | Purpose |
|----------|-------|---------|
| Employee Advance Status | 150 | Updates advance record statuses |

## Technical Implementation

### Patch Execution Order
1. **Pre-Model Sync:** Currently no patches configured
2. **Post-Model Sync:** All patches execute after database schema updates

### Error Handling Strategy
- Each patch includes error handling for data inconsistencies
- Rollback mechanisms for critical data operations
- Logging for troubleshooting and audit purposes

### Performance Considerations
- Patches designed for batch processing of large datasets
- Memory-efficient processing for employee advance updates
- Database optimization to minimize execution time

## Business Impact Analysis

### Employee Advance Management
The extensive employee advance status patches indicate:
- **Critical Business Process:** Employee advances are central to CSF TZ operations
- **Data Integrity Priority:** Significant effort invested in ensuring accurate advance tracking
- **Compliance Requirements:** Proper status tracking likely required for financial reporting

### System Customization
- **Extensive Customization:** Large number of custom fields indicates deep ERPNext customization
- **Business-Specific Requirements:** Property setters show adaptation to specific business needs
- **Security Focus:** Permission cleanup demonstrates attention to access control

## Maintenance and Monitoring

### Patch Success Verification
- Monitor patch execution logs during upgrades
- Verify employee advance data integrity post-migration
- Test custom field functionality after updates

### Future Patch Development
- Follow established patterns for employee advance patches
- Include comprehensive error handling and logging
- Test patches in staging environment before production

### Troubleshooting Guidelines
1. **Failed Employee Advance Patches:** Check data consistency and foreign key constraints
2. **Custom Field Issues:** Verify field dependencies and validation rules
3. **Permission Problems:** Review custom DocPerm cleanup results

## Related Documentation
- [CSF TZ Custom Fields Documentation](CSF_TZ_Custom_Fields_Documentation.md)
- [CSF TZ Property Setters Documentation](CSF_TZ_Property_Setters_Documentation.md)
- [CSF TZ DocTypes Documentation](CSF_TZ_DocTypes_Documentation.md)