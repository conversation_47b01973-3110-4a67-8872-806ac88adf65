# Frappe/ERPNext Notification System - Complete Documentation

## Table of Contents
1. [Introduction](#introduction)
2. [Core Concepts](#core-concepts)
3. [Notification Fields Explained](#notification-fields-explained)
4. [Event Types](#event-types)
5. [Communication Channels](#communication-channels)
6. [Recipients Configuration](#recipients-configuration)
7. [Message Templates](#message-templates)
8. [Real-World Examples](#real-world-examples)
9. [Advanced Features](#advanced-features)
10. [Best Practices](#best-practices)
11. [Troubleshooting](#troubleshooting)

---

## 1. Introduction

The Notification DocType in Frappe is a powerful automation tool that enables you to send automated alerts and messages based on document events. It supports multiple communication channels including Email, SMS, Slack, and System Notifications.

### Key Features:
- **Event-driven triggers**: Respond to document lifecycle events
- **Multiple channels**: Email, SMS, Slack, System Notifications
- **Dynamic content**: Use Jinja2 templating for personalized messages
- **Conditional logic**: Send notifications only when specific conditions are met
- **Role-based recipients**: Target users based on their roles
- **Attachments**: Include PDF prints of documents

---

## 2. Core Concepts

### What is a Notification?

A Notification is a configurable alert system that:
- Monitors specific document types for events
- Evaluates conditions to determine if notification should be sent
- Sends messages to designated recipients through configured channels

### Architecture Overview

```
Document Event → Notification Check → Condition Evaluation → Recipient Resolution → Message Rendering → Channel Dispatch
```

### Key Components:

1. **Document Type**: The DocType being monitored
2. **Event Trigger**: What action triggers the notification
3. **Condition**: Optional filter to control when notifications are sent
4. **Recipients**: Who receives the notification
5. **Message**: The content being sent
6. **Channel**: How the notification is delivered

---

## 3. Notification Fields Explained

### Basic Configuration

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| **Enabled** | Check | Activates/deactivates the notification | ✓ Checked |
| **Name** | Data | Unique identifier for the notification | "Sales Order Approval" |
| **Module** | Link | Module where notification belongs | "Selling" |
| **Is Standard** | Check | Marks as system notification (not editable) | ☐ Unchecked |

### Trigger Configuration

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| **Document Type** | Link | DocType to monitor | "Sales Order" |
| **Send Alert On** | Select | Event that triggers notification | "Submit" |
| **Condition** | Code | Python expression for additional filtering | `doc.grand_total > 10000` |

### Channel Configuration

| Field | Type | Description | Options |
|-------|------|-------------|---------|
| **Channel** | Select | Communication method | Email, Slack, System Notification, SMS |
| **Subject** | Data | Email/Notification subject line | "Order {{doc.name}} Submitted" |
| **Slack Webhook URL** | Link | For Slack notifications | Link to Slack Webhook URL |

### Message Configuration

| Field | Type | Description |
|-------|------|-------------|
| **Message** | Code | Main content using Jinja2 | See examples below |
| **Message Type** | Select | Format of message | HTML, Markdown, Plain Text |
| **Attach Print** | Check | Include PDF attachment |
| **Print Format** | Link | Specific print format to use |

---

## 4. Event Types

### 1. **New** - Document Creation
Triggers when a new document is created and saved for the first time.

**Use Case**: Welcome email for new customers
```python
# Condition example
doc.customer_type == "New"
```

### 2. **Save** - Document Update
Triggers every time a document is saved (including first save).

**Use Case**: Notify manager of quote updates
```python
# Condition example
doc.status == "Draft" and doc.modified_by != doc.owner
```

### 3. **Submit** - Document Submission
Triggers when a document is submitted (workflow completion).

**Use Case**: Order confirmation to customer
```python
# Condition example
doc.docstatus == 1 and doc.grand_total > 5000
```

### 4. **Cancel** - Document Cancellation
Triggers when a submitted document is cancelled.

**Use Case**: Alert finance team of cancelled invoices
```python
# Condition example
doc.docstatus == 2
```

### 5. **Days Before** - Date-based (Before)
Triggers specified days before a date field.

**Configuration**:
- **Reference Date**: Field containing the date (e.g., "due_date")
- **Days Before or After**: Number of days before (positive number)

**Use Case**: Payment reminder 3 days before due date
```python
# Settings
Reference Date: due_date
Days Before: 3
# Condition
doc.outstanding_amount > 0
```

### 6. **Days After** - Date-based (After)
Triggers specified days after a date field.

**Use Case**: Follow-up 7 days after delivery
```python
# Settings
Reference Date: delivery_date
Days After: 7
# Condition
doc.customer_feedback == None
```

### 7. **Value Change** - Field Modification
Triggers when a specific field value changes.

**Configuration**:
- **Value Changed**: Field to monitor (e.g., "status")

**Use Case**: Status change notifications
```python
# Settings
Value Changed: status
# Condition
doc.status in ["In Progress", "Completed"]
```

### 8. **Method** - Custom Trigger
Triggers on specific document methods.

**Available Methods**:
- `before_insert`
- `after_insert`
- `before_save`
- `validate`
- `on_update`
- `on_submit`
- `on_cancel`
- `on_trash`

**Use Case**: Complex validation notifications
```python
# Settings
Trigger Method: validate
# Condition
len(doc.items) > 10
```

### 9. **Custom** - Manual Trigger
For notifications triggered programmatically.

---

## 5. Communication Channels

### Email Channel

**Features**:
- HTML/Plain text messages
- File attachments
- CC/BCC support
- Custom sender configuration

**Configuration Example**:
```yaml
Channel: Email
Sender: <EMAIL>
Subject: Order #{{doc.name}} Confirmation
Attach Print: Yes
Print Format: Sales Order Standard
```

### Slack Channel

**Features**:
- Real-time messaging
- Rich formatting with Markdown
- Channel/user mentions

**Setup Requirements**:
1. Create Slack Webhook URL in Slack App
2. Add Webhook URL in Frappe
3. Link in Notification

**Message Example**:
```markdown
*New Order Alert* 🎉
Customer: {{doc.customer_name}}
Amount: ${{doc.grand_total}}
View: {{frappe.utils.get_url_to_form(doc.doctype, doc.name)}}
```

### System Notification

**Features**:
- In-app notifications
- No external dependencies
- Instant delivery
- Notification dropdown

**Use Cases**:
- Task assignments
- Approval requests
- System alerts

### SMS Channel

**Features**:
- Short messages (160 chars)
- Direct to mobile
- High open rates

**Configuration Requirements**:
- SMS Settings configured
- Valid SMS gateway
- Mobile number field

---

## 6. Recipients Configuration

### Recipient Types

#### 1. Document Field Recipients
Select recipients from document fields containing email addresses.

**Single Field**:
```yaml
Receiver By Document Field: customer_email
```

**Multiple Fields (Child Table)**:
```yaml
Receiver By Document Field: email_id,sales_team
# Gets email_id from each row in sales_team child table
```

#### 2. Role-Based Recipients
Send to all users with specific role.

```yaml
Receiver By Role: Sales Manager
```

#### 3. Static Recipients
Fixed email addresses in CC/BCC fields.

**CC Field**:
```
<EMAIL>
<EMAIL>
```

#### 4. Assignee Recipients
Send to all users assigned to the document.

```yaml
Send To All Assignees: ✓
```

### Conditional Recipients

Add conditions to recipient rows:

```python
# Recipient Condition Examples
doc.priority == "High"  # Only for high priority
doc.grand_total > 10000  # Only for large orders
frappe.utils.nowdate() == doc.delivery_date  # Same day delivery
```

---

## 7. Message Templates

### Jinja2 Template Basics

**Variables Available**:
- `doc`: Current document object
- `frappe.utils`: Utility functions
- `nowdate`: Current date function
- Custom context from standard notifications

### HTML Message Example

```html
<h3>Order Confirmation</h3>

<p>Dear {{doc.customer_name}},</p>

<p>Your order <strong>#{{doc.name}}</strong> has been confirmed.</p>

<h4>Order Details:</h4>
<table border="1" style="border-collapse: collapse;">
    <tr>
        <th>Item</th>
        <th>Quantity</th>
        <th>Rate</th>
        <th>Amount</th>
    </tr>
    {% for item in doc.items %}
    <tr>
        <td>{{item.item_name}}</td>
        <td>{{item.qty}}</td>
        <td>${{item.rate}}</td>
        <td>${{item.amount}}</td>
    </tr>
    {% endfor %}
</table>

<p><strong>Total: ${{doc.grand_total}}</strong></p>

<p>Expected Delivery: {{doc.delivery_date}}</p>

{% if doc.notes %}
<p>Special Instructions: {{doc.notes}}</p>
{% endif %}

<p>Thank you for your business!</p>
```

### Markdown Message Example

```markdown
## Task Assignment

**Task:** {{doc.subject}}
**Assigned to:** {{doc.allocated_to}}
**Due Date:** {{doc.date}}

### Description:
{{doc.description}}

**Priority:** {{doc.priority}}
**Status:** {{doc.status}}

---
[View Task]({{frappe.utils.get_url_to_form(doc.doctype, doc.name)}})
```

### Plain Text Message Example

```text
PAYMENT REMINDER

Invoice No: {{doc.name}}
Customer: {{doc.customer_name}}
Amount Due: ${{doc.outstanding_amount}}
Due Date: {{doc.due_date}}

Days Overdue: {{frappe.utils.date_diff(nowdate(), doc.due_date)}}

Please make payment at your earliest convenience.

Thank you.
```

---

## 8. Real-World Examples

### Example 1: Sales Order Approval Notification

**Scenario**: Notify sales manager when orders exceed $10,000

```yaml
Name: High Value Order Approval
Document Type: Sales Order
Event: Submit
Channel: Email
Condition: doc.grand_total > 10000
Subject: Approval Required: Order {{doc.name}} - ${{doc.grand_total}}
Recipients: 
  - Receiver By Role: Sales Manager
  - CC: <EMAIL>
```

**Message**:
```html
<h3>High Value Order Requires Approval</h3>

<p>A new order exceeding $10,000 has been submitted:</p>

<ul>
    <li><strong>Order Number:</strong> {{doc.name}}</li>
    <li><strong>Customer:</strong> {{doc.customer_name}}</li>
    <li><strong>Total Value:</strong> ${{doc.grand_total}}</li>
    <li><strong>Sales Person:</strong> {{doc.sales_person}}</li>
    <li><strong>Margin:</strong> {{doc.margin_rate_or_amount}}%</li>
</ul>

<p><a href="{{frappe.utils.get_url_to_form('Sales Order', doc.name)}}">Review and Approve Order</a></p>
```

### Example 2: Payment Reminder Series

**First Reminder - 3 Days Before Due**:
```yaml
Name: Payment Reminder - 3 Days
Document Type: Sales Invoice
Event: Days Before
Reference Date: due_date
Days Before: 3
Channel: Email
Condition: doc.outstanding_amount > 0
Subject: Payment Reminder: Invoice {{doc.name}} due in 3 days
Recipients:
  - Receiver By Document Field: customer_email
```

**Second Reminder - On Due Date**:
```yaml
Name: Payment Due Today
Document Type: Sales Invoice
Event: Days Before
Reference Date: due_date
Days Before: 0
Channel: Email + System Notification
Condition: doc.outstanding_amount > 0
Subject: Payment Due Today: Invoice {{doc.name}}
```

**Overdue Notice - 7 Days After**:
```yaml
Name: Overdue Payment Notice
Document Type: Sales Invoice
Event: Days After
Reference Date: due_date
Days After: 7
Channel: Email
Condition: doc.outstanding_amount > 0
Subject: OVERDUE: Invoice {{doc.name}} - Immediate Action Required
Recipients:
  - Receiver By Document Field: customer_email
  - Receiver By Role: Accounts Manager
```

### Example 3: Customer Onboarding Workflow

**Welcome Email**:
```yaml
Name: Customer Welcome
Document Type: Customer
Event: New
Channel: Email
Subject: Welcome to {{doc.company}}!
Recipients:
  - Receiver By Document Field: email_id
```

**Message**:
```html
<h2>Welcome {{doc.customer_name}}!</h2>

<p>Thank you for choosing us as your partner. Your account has been successfully created.</p>

<h3>Your Account Details:</h3>
<ul>
    <li>Customer ID: {{doc.name}}</li>
    <li>Account Type: {{doc.customer_type}}</li>
    <li>Credit Limit: ${{doc.credit_limit or "Not Set"}}</li>
</ul>

<h3>Your Account Manager:</h3>
<p>{{doc.account_manager or "Will be assigned soon"}}</p>

<h3>Next Steps:</h3>
<ol>
    <li>Complete your profile information</li>
    <li>Review our product catalog</li>
    <li>Place your first order to receive 10% discount</li>
</ol>

<p>If you have any questions, please don't hesitate to contact us.</p>
```

### Example 4: Task Management with Slack

**Task Assignment Notification**:
```yaml
Name: Task Assignment Alert
Document Type: Task
Event: New
Channel: Slack
Slack Webhook URL: [Your Webhook]
Condition: doc.status == "Open"
```

**Slack Message**:
```markdown
:clipboard: *New Task Assigned*

*Task:* {{doc.subject}}
*Project:* {{doc.project or "None"}}
*Assigned to:* @{{doc.allocated_to}}
*Priority:* {{":fire:" if doc.priority == "High" else ":arrow_right:"}} {{doc.priority}}
*Due:* {{doc.exp_end_date}}

*Description:*
> {{doc.description[:200]}}{{" ..." if len(doc.description) > 200 else ""}}

<{{frappe.utils.get_url_to_form('Task', doc.name)}}|View Task>
```

### Example 5: Inventory Alert System

**Low Stock Alert**:
```yaml
Name: Low Stock Alert
Document Type: Item
Event: Value Change
Value Changed: actual_qty
Channel: Email + System Notification
Condition: doc.actual_qty < doc.reorder_level
Subject: Low Stock Alert: {{doc.item_name}}
Recipients:
  - Receiver By Role: Purchase Manager
  - Receiver By Role: Stock Manager
```

**Message**:
```html
<div style="background: #fff3cd; border: 1px solid #ffc107; padding: 15px; border-radius: 5px;">
    <h3 style="color: #856404;">⚠️ Low Stock Alert</h3>
    
    <p>The following item has reached its reorder level:</p>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td><strong>Item Code:</strong></td>
            <td>{{doc.item_code}}</td>
        </tr>
        <tr>
            <td><strong>Item Name:</strong></td>
            <td>{{doc.item_name}}</td>
        </tr>
        <tr>
            <td><strong>Current Stock:</strong></td>
            <td style="color: red;">{{doc.actual_qty}} {{doc.stock_uom}}</td>
        </tr>
        <tr>
            <td><strong>Reorder Level:</strong></td>
            <td>{{doc.reorder_level}} {{doc.stock_uom}}</td>
        </tr>
        <tr>
            <td><strong>Reorder Quantity:</strong></td>
            <td>{{doc.reorder_qty}} {{doc.stock_uom}}</td>
        </tr>
    </table>
    
    <p><strong>Action Required:</strong> Please create a purchase order for this item.</p>
    
    <p>
        <a href="{{frappe.utils.get_url_to_form('Item', doc.name)}}" 
           style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            View Item Details
        </a>
    </p>
</div>
```

### Example 6: Employee Lifecycle Notifications

**Leave Application Status Update**:
```yaml
Name: Leave Application Status Update
Document Type: Leave Application
Event: Value Change
Value Changed: status
Channel: Email + System Notification
Condition: doc.status in ["Approved", "Rejected"]
Subject: Leave Application {{doc.status}}: {{doc.employee_name}}
Recipients:
  - Receiver By Document Field: employee
  - CC: doc.leave_approver
```

**Message with Conditional Content**:
```html
<h3>Leave Application {{doc.status}}</h3>

<p>Dear {{doc.employee_name}},</p>

{% if doc.status == "Approved" %}
<p style="color: green;">✅ Your leave application has been approved!</p>
{% else %}
<p style="color: red;">❌ Your leave application has been rejected.</p>
{% endif %}

<h4>Leave Details:</h4>
<ul>
    <li><strong>Leave Type:</strong> {{doc.leave_type}}</li>
    <li><strong>From Date:</strong> {{doc.from_date}}</li>
    <li><strong>To Date:</strong> {{doc.to_date}}</li>
    <li><strong>Total Days:</strong> {{doc.total_leave_days}}</li>
    <li><strong>Reason:</strong> {{doc.description}}</li>
    {% if doc.status == "Approved" %}
    <li><strong>Approved By:</strong> {{doc.leave_approver}}</li>
    {% else %}
    <li><strong>Rejected By:</strong> {{doc.leave_approver}}</li>
    <li><strong>Rejection Reason:</strong> {{doc.rejection_reason or "Not specified"}}</li>
    {% endif %}
</ul>

{% if doc.status == "Approved" %}
<p>Please ensure all your pending work is handed over before your leave.</p>
{% else %}
<p>If you have any questions about this decision, please contact HR.</p>
{% endif %}

<p>Best regards,<br>HR Department</p>
```

---

## 9. Advanced Features

### Set Property After Alert

Automatically update a field after sending notification.

**Use Case**: Mark invoice as "Reminder Sent"
```yaml
Set Property After Alert: reminder_sent
Property Value: 1
```

### Using Comments in Notifications

Include document comments in notifications:

```html
{% if comments %}
<h4>Recent Comments:</h4>
{% for comment in comments[-3:] %}
<div style="border-left: 3px solid #ccc; padding-left: 10px; margin: 10px 0;">
    <strong>{{comment.by}}:</strong> {{comment.comment}}
    <br><small>{{comment.creation}}</small>
</div>
{% endfor %}
{% endif %}
```

### Dynamic Recipient Resolution

Using Python expressions for complex recipient logic:

```python
# In recipient condition field
frappe.db.get_value("Customer", doc.customer, "customer_type") == "Premium"
```

### Custom Context in Standard Notifications

Create a Python file alongside your notification:

```python
# notification_name.py
import frappe

def get_context(context):
    # Add custom data to context
    context["total_orders"] = frappe.db.count("Sales Order", 
        {"customer": context["doc"].customer})
    
    context["customer_since"] = frappe.db.get_value("Customer", 
        context["doc"].customer, "creation")
    
    return context
```

### Notification Chains

Create sequences of notifications:

1. **Initial Alert**: Immediate notification on event
2. **Reminder**: Days Before/After notifications
3. **Escalation**: Condition-based escalations

### Performance Optimization

For high-volume notifications:

```python
# Condition to limit notifications
frappe.utils.now_datetime().hour >= 9 and frappe.utils.now_datetime().hour <= 18
# Only send during business hours
```

---

## 10. Best Practices

### 1. Naming Conventions

Use descriptive, systematic names:
- `[DocType] - [Event] - [Purpose]`
- Example: `Sales Order - Submit - Customer Confirmation`

### 2. Testing Strategy

1. Create test documents in development
2. Use test email addresses
3. Verify conditions with different scenarios
4. Check all recipient types
5. Test with print attachments

### 3. Performance Considerations

- **Avoid heavy computations in conditions**
- **Limit recipients to necessary users**
- **Use appropriate events** (not Save for everything)
- **Batch similar notifications**

### 4. Message Design

- **Keep subjects concise** (50 chars max)
- **Use HTML for rich formatting**
- **Include clear CTAs** (Call to Action)
- **Make messages mobile-friendly**
- **Test rendering in different clients**

### 5. Error Handling

Always include fallbacks:
```html
{{doc.customer_name or "Valued Customer"}}
{{doc.delivery_date or "To be confirmed"}}
```

### 6. Security Considerations

- **Never expose sensitive data** in notifications
- **Use role-based recipients** for confidential info
- **Validate email addresses** before sending
- **Audit notification logs** regularly

### 7. Documentation

Document your notifications:
```yaml
# In notification description/notes
Purpose: Send payment reminder 3 days before due date
Recipients: Customer primary contact
Frequency: Once per invoice
Dependencies: SMS Settings must be configured
```

---

## 11. Troubleshooting

### Common Issues and Solutions

#### 1. Notification Not Triggering

**Check**:
- [ ] Notification is enabled
- [ ] Document type is correct
- [ ] Event matches your use case
- [ ] Condition evaluates to True
- [ ] No Python errors in condition

**Debug**:
```python
# Add to condition for testing
frappe.log_error(f"Notification Debug: {doc.name}", "Notification Test")
True  # Always trigger for testing
```

#### 2. Recipients Not Receiving

**Check**:
- [ ] Email addresses are valid
- [ ] Email queue is processing
- [ ] SMTP settings are correct
- [ ] Recipients have correct roles
- [ ] No conditions blocking recipients

**Verify Email Queue**:
```sql
SELECT * FROM `tabEmail Queue` 
WHERE reference_doctype = 'Your DocType' 
ORDER BY creation DESC;
```

#### 3. Template Errors

**Common Issues**:
- Missing variables: Use `{{doc.field or ""}}`
- Syntax errors: Check Jinja2 syntax
- Invalid HTML: Validate HTML structure

**Test Template**:
```python
# In Python console
doc = frappe.get_doc("DocType", "doc_name")
context = {"doc": doc}
result = frappe.render_template(template_string, context)
print(result)
```

#### 4. Performance Issues

**Solutions**:
- Add indexes to filtered fields
- Optimize condition expressions
- Reduce recipient list size
- Use async email sending
- Implement notification queuing

#### 5. Slack Integration Issues

**Check**:
- [ ] Webhook URL is valid
- [ ] Slack app has permissions
- [ ] Message format is correct
- [ ] Network connectivity

### Error Logs

Check logs at:
- **Email Queue**: Automatic Emails not sent
- **Error Log**: System errors
- **Notification Log**: Sent notifications
- **Scheduled Job Log**: Daily notification runs

### Testing Checklist

Before deploying a notification:

1. **Functionality**
   - [ ] Triggers on correct event
   - [ ] Condition works as expected
   - [ ] All recipient types tested
   - [ ] Message renders correctly

2. **Content**
   - [ ] No spelling/grammar errors
   - [ ] Links work correctly
   - [ ] Dynamic content displays
   - [ ] Formatting is consistent

3. **Performance**
   - [ ] Tested with bulk operations
   - [ ] No timeout issues
   - [ ] Email queue processes normally

4. **Edge Cases**
   - [ ] Null/empty values handled
   - [ ] Special characters in data
   - [ ] Very long content
   - [ ] Multiple recipients

---

## Appendix A: Useful Code Snippets

### Get User's Full Name
```python
{{frappe.db.get_value("User", doc.owner, "full_name")}}
```

### Format Currency
```python
{{frappe.utils.fmt_money(doc.grand_total, currency=doc.currency)}}
```

### Calculate Days Between
```python
{{frappe.utils.date_diff(doc.delivery_date, doc.order_date)}} days
```

### Get Document URL
```python
{{frappe.utils.get_url_to_form(doc.doctype, doc.name)}}
```

### Conditional CSS Classes
```html
<div class="{{'urgent' if doc.priority == 'High' else 'normal'}}">
    Content
</div>
```

### Loop Through Child Table
```html
{% for row in doc.items %}
    {{row.item_name}}: {{row.qty}} x {{row.rate}} = {{row.amount}}
{% endfor %}
```

### Include Another Template
```python
{% include "templates/includes/footer.html" %}
```

---

## Appendix B: Available Variables Reference

### In Conditions and Messages:

| Variable | Description | Example Usage |
|----------|-------------|---------------|
| `doc` | Current document object | `doc.customer_name` |
| `frappe` | Frappe framework object | `frappe.utils.nowdate()` |
| `nowdate` | Current date function | `nowdate()` |
| `nowtime` | Current time function | `nowtime()` |
| `comments` | Document comments | `comments[-1].comment` |

### Useful Frappe Utils:

| Function | Description | Example |
|----------|-------------|---------|
| `get_url()` | Get site URL | `frappe.utils.get_url()` |
| `fmt_money()` | Format currency | `frappe.utils.fmt_money(1000)` |
| `date_diff()` | Days between dates | `frappe.utils.date_diff(date1, date2)` |
| `add_days()` | Add days to date | `frappe.utils.add_days(date, 10)` |
| `get_fullname()` | Get user's full name | `frappe.utils.get_fullname(email)` |
| `cstr()` | Convert to string | `frappe.utils.cstr(value)` |
| `flt()` | Convert to float | `frappe.utils.flt(value)` |
| `cint()` | Convert to integer | `frappe.utils.cint(value)` |

---

## Appendix C: Email HTML Template Boilerplate

```html
<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background: white;
            padding: 30px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 5px 5px;
            font-size: 12px;
            color: #6c757d;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>{{doc.company}}</h2>
        </div>
        
        <div class="content">
            <!-- Your content here -->
            <h3>{{subject}}</h3>
            
            <p>Dear {{doc.customer_name or "Customer"}},</p>
            
            <!-- Main message content -->
            {{message_content}}
            
            <!-- Call to action -->
            <div style="text-align: center;">
                <a href="{{link}}" class="button">Action Button</a>
            </div>
        </div>
        
        <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© {{nowdate()[:4]}} {{doc.company}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
```

---

## Conclusion

The Frappe Notification system is a powerful tool for automating communications in your ERP system. By understanding its components and following best practices, you can create effective, maintainable notification workflows that enhance user experience and operational efficiency.

### Key Takeaways:

1. **Start simple**: Begin with basic notifications and add complexity as needed
2. **Test thoroughly**: Always test in development before production
3. **Monitor performance**: Keep an eye on notification volumes and system impact
4. **Document well**: Maintain clear documentation for your notification setup
5. **Iterate and improve**: Regularly review and optimize your notifications

### Further Resources:

- [Frappe Framework Documentation](https://frappeframework.com/docs)
- [Jinja2 Template Documentation](https://jinja.palletsprojects.com/)
- [ERPNext User Manual](https://docs.erpnext.com/)

---

*Document Version: 1.0*  
*Last Updated: 2024*  
*Compatible with: Frappe Framework v14+*
