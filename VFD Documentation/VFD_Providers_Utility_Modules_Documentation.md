# VFD Providers - Utility Modules Documentation

## Overview

This document provides comprehensive information about utility modules and independent scripts in the VFD Providers application. These modules contain business logic, API integrations, and helper functions that support VFD functionality.

## Utility Modules Structure

### Directory Layout
```
apps/vfd_providers/vfd_providers/utils/
├── __pycache__/
├── customer.js
├── sales_invoice.js
├── sales_invoice.py
└── utils.py
```

## Python Utility Modules

### 1. Main Utils Module (utils.py)

**Location:** `apps/vfd_providers/vfd_providers/utils/utils.py`
**Purpose:** Core VFD processing functions and business logic

#### Key Functions

##### generate_tra_vfd()
```python
@frappe.whitelist()
def generate_tra_vfd(docname, sinv_doc=None, method="POST")
```
**Purpose:** Main function to generate VFD receipts for sales invoices
**Parameters:**
- `docname`: Sales Invoice document name
- `sinv_doc`: Optional Sales Invoice document object
- `method`: HTTP method for API call

**Business Logic:**
1. Validates invoice eligibility for VFD processing
2. Retrieves company VFD provider configuration
3. Routes to appropriate VFD provider based on settings
4. Supports VFDPlus, TotalVFD, and SimplifyVFD providers

##### autogenerate_vfd()
```python
def autogenerate_vfd(doc, method)
```
**Purpose:** Automatically generates VFD when invoice is submitted
**Trigger:** Called from Sales Invoice `on_submit` hook
**Conditions:**
- Invoice is not marked as non-VFD
- VFD status is not already "Success"
- Invoice is not a return document
- Auto-generation flag is enabled

##### posting_all_vfd_invoices()
```python
def posting_all_vfd_invoices()
```
**Purpose:** Batch processes pending VFD invoices across all companies
**Schedule:** Runs every 15 minutes via cron job
**Process:**
1. Iterates through all companies
2. Finds invoices with pending/failed VFD status
3. Attempts VFD posting for each eligible invoice
4. Updates invoice status based on results

##### clean_and_update_tax_id_info()
```python
def clean_and_update_tax_id_info(doc, method)
```
**Purpose:** Standardizes customer tax ID information for VFD compliance
**Processing:**
- Removes non-numeric characters from tax ID
- Sets VFD customer ID type based on tax ID presence
- Assigns default values for customers without tax ID

### 2. Sales Invoice Utils Module (sales_invoice.py)

**Location:** `apps/vfd_providers/vfd_providers/utils/sales_invoice.py`
**Purpose:** Sales Invoice-specific VFD validations and processing

#### Key Functions

##### vfd_validation()
```python
def vfd_validation(doc, method)
```
**Purpose:** Validates VFD requirements before invoice submission
**Validations:**
- Ensures base net total is not zero
- Validates tax configuration
- Checks VFD provider settings
- Verifies customer VFD ID information

##### validate_cancel()
```python
def validate_cancel(doc, method)
```
**Purpose:** Prevents cancellation of VFD-submitted invoices
**Logic:** Throws error if invoice has VFD receipt number (already sent to TRA)

##### get_customer_id_info()
```python
def get_customer_id_info(customer)
```
**Purpose:** Retrieves and validates customer VFD identification information
**Returns:** Dictionary with customer ID, ID type, and mobile number

##### get_itemised_tax_breakup_html()
```python
def get_itemised_tax_breakup_html(doc)
```
**Purpose:** Calculates itemized tax breakdown for VFD submission
**Returns:** Tax breakdown data required by VFD providers

#### Utility Functions

##### remove_special_characters()
```python
def remove_special_characters(text)
```
**Purpose:** Cleans text data for VFD API compatibility

##### remove_all_except_numbers()
```python
def remove_all_except_numbers(text=None)
```
**Purpose:** Extracts only numeric characters from text fields

### 3. VFD Provider Utils Module (vfd_providers/utils.py)

**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/utils.py`
**Purpose:** VFD-specific calculation utilities

#### Key Functions

##### get_vat_amount()
```python
def get_vat_amount(item, vat_group, precision=0)
```
**Purpose:** Calculates VAT amount for VFD submission based on tax group
**Parameters:**
- `item`: Invoice item object
- `vat_group`: VAT group classification (A/1 for standard rate)
- `precision`: Decimal precision for calculations

**Logic:**
- Handles VAT-inclusive and VAT-exclusive amounts
- Applies 18% standard rate for group A/1
- Returns precise VAT calculations for VFD compliance

## JavaScript Client-Side Modules

### 1. Sales Invoice JavaScript (sales_invoice.js)

**Location:** `apps/vfd_providers/vfd_providers/utils/sales_invoice.js`
**Purpose:** Enhances Sales Invoice form with VFD functionality

#### Key Features
- **VFD Generation Button:** Adds custom button for manual VFD generation
- **Form Validations:** Client-side validation for VFD requirements
- **Status Display:** Enhanced VFD status indicators
- **Customer Integration:** Auto-populates customer VFD information

#### Implementation Pattern
```javascript
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // Add VFD generation button
        // Update form based on VFD status
        // Handle conditional field visibility
    },

    customer: function(frm) {
        // Auto-populate VFD customer information
        // Validate customer VFD requirements
    }
});
```

### 2. Customer JavaScript (customer.js)

**Location:** `apps/vfd_providers/vfd_providers/utils/customer.js`
**Purpose:** Enhances Customer form with VFD-specific functionality

#### Key Features
- **Tax ID Validation:** Real-time validation of tax identification numbers
- **VFD ID Type Selection:** Smart selection based on ID format
- **Form Enhancements:** Improved user experience for VFD data entry

## API Integration Modules

### VFD Provider Implementations

#### 1. SimplifyVFD Integration
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/simplify_vfd_settings/`

**Key Features:**
- Token-based authentication
- Automatic token refresh
- RESTful API integration
- Error handling and retry logic

#### 2. TotalVFD Integration
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/total_vfd_setting/`

**Key Features:**
- Bearer token authentication
- JSON payload formatting
- Receipt verification URLs
- Real-time status updates

#### 3. VFDPlus Integration
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/vfdplus_settings/`

**Key Features:**
- Legacy API support
- XML/JSON hybrid communication
- Backward compatibility
- Migration support

## Data Processing Utilities

### Tax Calculation Functions
- **Itemized Tax Breakdown:** Calculates tax per item for VFD submission
- **VAT Amount Calculation:** Handles VAT-inclusive/exclusive scenarios
- **Tax Code Mapping:** Maps Frappe tax templates to VFD tax codes

### Data Validation Functions
- **Customer ID Validation:** Ensures valid customer identification
- **Invoice Validation:** Checks invoice completeness for VFD
- **Provider Validation:** Verifies VFD provider configuration

### Data Formatting Functions
- **Text Cleaning:** Removes special characters for API compatibility
- **Number Extraction:** Extracts numeric values from mixed text
- **Date/Time Formatting:** Formats timestamps for VFD APIs

## Error Handling and Logging

### Error Management
- **Graceful Degradation:** Continues processing when non-critical errors occur
- **User-Friendly Messages:** Provides clear error messages to users
- **Retry Mechanisms:** Automatic retry for transient failures

### Logging Strategy
- **VFD Posting Logs:** Tracks all VFD submission attempts
- **Error Logs:** Records detailed error information
- **Performance Logs:** Monitors API response times

## Configuration Management

### Environment Support
- **Development Mode:** Sandbox API endpoints
- **Production Mode:** Live VFD provider APIs
- **Testing Mode:** Mock responses for unit testing

### Company-Specific Settings
- **Multi-Company Support:** Different VFD providers per company
- **Provider Selection:** Dynamic provider routing
- **Configuration Validation:** Ensures proper setup

## Performance Optimization

### Caching Strategies
- **Provider Settings Cache:** Reduces database queries
- **Customer Data Cache:** Improves form loading speed
- **Token Cache:** Minimizes authentication requests

### Batch Processing
- **Bulk VFD Posting:** Processes multiple invoices efficiently
- **Queue Management:** Handles high-volume scenarios
- **Resource Management:** Optimizes memory and CPU usage

## Related Documentation
- [VFD Providers DocTypes Documentation](VFD_Providers_DocTypes_Documentation.md)
- [VFD Providers Hooks Documentation](VFD_Providers_Hooks_Documentation.md)
- [VFD Providers Patch Files Documentation](VFD_Providers_Patch_Files_Documentation.md)