<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FrappeClient Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 2.5em;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        h4 {
            color: #34495e;
            margin-top: 25px;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        .overview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .overview h2 {
            color: white;
            border-left: 4px solid white;
            margin-top: 0;
        }
        code {
            background-color: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        pre code {
            background: none;
            color: #ecf0f1;
            padding: 0;
        }
        .method-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .use-case {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .warning {
            background: #fdf2e9;
            border-left: 4px solid #e67e22;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .best-practice {
            background: #eaf4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin-bottom: 8px;
        }
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            color: #3498db;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body {
                background-color: white;
                font-size: 12pt;
            }
            .container {
                box-shadow: none;
                padding: 20px;
            }
            pre {
                font-size: 10pt;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FrappeClient Documentation</h1>
        
        <div class="overview">
            <h2>Overview</h2>
            <p><strong>FrappeClient</strong> is a Python library that provides a programmatic interface to connect and interact with remote Frappe/ERPNext systems via HTTP API calls. It acts as a bridge between different Frappe instances, enabling seamless data exchange, synchronization, and remote operations.</p>
        </div>

        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#what-it-solves">1. What It Solves</a></li>
                <li><a href="#core-classes">2. Core Classes</a></li>
                <li><a href="#authentication">3. Authentication Methods</a></li>
                <li><a href="#core-operations">4. Core Operations</a></li>
                <li><a href="#real-world-examples">5. Real-World Use Cases</a></li>
                <li><a href="#when-to-use">6. When to Use FrappeClient</a></li>
                <li><a href="#best-practices">7. Best Practices</a></li>
                <li><a href="#performance">8. Performance Considerations</a></li>
                <li><a href="#security">9. Security Best Practices</a></li>
            </ul>
        </div>

        <h2 id="what-it-solves">What It Solves</h2>
        
        <h3>Primary Problems:</h3>
        <ul>
            <li><strong>System Integration</strong>: Connects multiple Frappe instances for data synchronization</li>
            <li><strong>Remote Operations</strong>: Performs CRUD operations on remote Frappe systems without direct database access</li>
            <li><strong>Data Migration</strong>: Facilitates moving data between different Frappe installations</li>
            <li><strong>API Abstraction</strong>: Simplifies complex HTTP API interactions with a clean Python interface</li>
            <li><strong>Authentication Management</strong>: Handles session management and API key authentication automatically</li>
        </ul>

        <h3>Key Benefits:</h3>
        <ul>
            <li><strong>Secure Communication</strong>: Uses proper authentication mechanisms</li>
            <li><strong>Error Handling</strong>: Comprehensive exception handling for common scenarios</li>
            <li><strong>Bulk Operations</strong>: Supports batch operations for better performance</li>
            <li><strong>Context Management</strong>: Automatic session cleanup with context managers</li>
            <li><strong>Flexible Authentication</strong>: Multiple authentication methods</li>
        </ul>

        <h2 id="core-classes">Core Classes</h2>
        
        <div class="method-box">
            <h3>1. FrappeClient</h3>
            <p>The main client class for interacting with Frappe systems.</p>
            
            <h3>2. FrappeOAuth2Client</h3>
            <p>OAuth2-based authentication client for secure integrations.</p>
            
            <h3>3. Custom Exceptions</h3>
            <ul>
                <li><code>AuthError</code>: Authentication failures</li>
                <li><code>SiteExpiredError</code>: Site subscription expired</li>
                <li><code>SiteUnreachableError</code>: Network connectivity issues</li>
                <li><code>FrappeException</code>: General API errors</li>
            </ul>
        </div>

        <div class="page-break"></div>

        <h2 id="authentication">Authentication Methods</h2>

        <h3>Method 1: Username/Password Authentication</h3>
        <pre><code>from frappe.frappeclient import FrappeClient

# Session-based authentication
client = FrappeClient(
    url="https://your-site.frappe.cloud",
    username="<EMAIL>",
    password="your_password"
)</code></pre>

        <h3>Method 2: API Key Authentication</h3>
        <pre><code># API key authentication (recommended for production)
client = FrappeClient(
    url="https://your-site.frappe.cloud",
    api_key="your_api_key",
    api_secret="your_api_secret"
)</code></pre>

        <h3>Method 3: OAuth2 Authentication</h3>
        <pre><code>from frappe.frappeclient import FrappeOAuth2Client

# OAuth2 authentication
oauth_client = FrappeOAuth2Client(
    url="https://your-site.frappe.cloud",
    access_token="your_oauth_token"
)</code></pre>

        <h2 id="core-operations">Core Operations</h2>

        <h3>1. Document Retrieval</h3>
        
        <h4>Get Single Document</h4>
        <pre><code># Get a specific document
customer = client.get_doc("Customer", "CUST-001")
print(customer.customer_name)

# Get document with specific fields
customer = client.get_doc(
    "Customer", 
    "CUST-001", 
    fields=["customer_name", "email_id", "mobile_no"]
)

# Get document by filters
customer = client.get_doc(
    "Customer", 
    filters={"email_id": "<EMAIL>"}
)</code></pre>

        <h4>Get Document Lists</h4>
        <pre><code># Get all customers
customers = client.get_list("Customer")

# Get customers with specific fields
customers = client.get_list(
    "Customer",
    fields=["name", "customer_name", "email_id"],
    filters={"disabled": 0},
    limit_page_length=50
)

# Advanced filtering
active_customers = client.get_list(
    "Customer",
    fields=["name", "customer_name", "territory"],
    filters={
        "disabled": 0,
        "territory": ["in", ["North", "South"]],
        "creation": [">=", "2024-01-01"]
    }
)</code></pre>

        <h3>2. Document Creation</h3>
        
        <h4>Insert Single Document</h4>
        <pre><code># Create a new customer
new_customer = {
    "doctype": "Customer",
    "customer_name": "ABC Corporation",
    "customer_type": "Company",
    "territory": "North",
    "email_id": "<EMAIL>"
}

result = client.insert(new_customer)
print(f"Created customer: {result.name}")</code></pre>

        <h4>Bulk Insert</h4>
        <pre><code># Insert multiple documents at once
customers = [
    {
        "doctype": "Customer",
        "customer_name": "Company A",
        "customer_type": "Company"
    },
    {
        "doctype": "Customer", 
        "customer_name": "Company B",
        "customer_type": "Company"
    }
]

results = client.insert_many(customers)</code></pre>

        <div class="page-break"></div>

        <h3>3. Document Updates</h3>
        
        <h4>Update Single Document</h4>
        <pre><code># Update customer details
customer_update = {
    "doctype": "Customer",
    "name": "CUST-001",
    "mobile_no": "******-0123",
    "website": "https://abc-corp.com"
}

client.update(customer_update)</code></pre>

        <h4>Bulk Update</h4>
        <pre><code># Update multiple documents
updates = [
    {
        "doctype": "Customer",
        "name": "CUST-001", 
        "territory": "West"
    },
    {
        "doctype": "Customer",
        "name": "CUST-002",
        "territory": "East"
    }
]

client.bulk_update(updates)</code></pre>

        <h4>Set Single Field Value</h4>
        <pre><code># Update a single field
client.set_value("Customer", "CUST-001", "territory", "Central")</code></pre>

        <h3>4. Document Workflow Operations</h3>
        
        <h4>Submit Documents</h4>
        <pre><code># Submit a sales order
sales_order = {
    "doctype": "Sales Order",
    "name": "SO-001"
}
client.submit(sales_order)</code></pre>

        <h4>Cancel Documents</h4>
        <pre><code># Cancel a sales order
client.cancel("Sales Order", "SO-001")</code></pre>

        <h3>5. Document Deletion</h3>
        <pre><code># Delete a document
client.delete("Customer", "CUST-001")</code></pre>

        <h3>6. Custom API Methods</h3>
        <pre><code># Call custom API methods
# GET request
result = client.get_api("myapp.api.get_dashboard_data", {
    "from_date": "2024-01-01",
    "to_date": "2024-12-31"
})

# POST request
result = client.post_api("myapp.api.process_bulk_data", {
    "data": json.dumps(bulk_data),
    "process_type": "import"
})</code></pre>

        <div class="page-break"></div>

        <h2 id="real-world-examples">Real-World Use Cases & Examples</h2>

        <div class="use-case">
            <h3>1. Multi-Site Data Synchronization</h3>
            <p><strong>Scenario</strong>: Synchronize customer data between headquarters and branch offices.</p>
        </div>

        <pre><code># Headquarters to Branch sync
def sync_customers_to_branch():
    # Connect to headquarters
    hq_client = FrappeClient(
        url="https://hq.company.com",
        api_key="hq_api_key",
        api_secret="hq_api_secret"
    )
    
    # Connect to branch
    branch_client = FrappeClient(
        url="https://branch1.company.com", 
        api_key="branch_api_key",
        api_secret="branch_api_secret"
    )
    
    # Get customers modified in last 24 hours
    from datetime import datetime, timedelta
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    modified_customers = hq_client.get_list(
        "Customer",
        fields=["*"],
        filters={"modified": [">=", yesterday]}
    )
    
    for customer in modified_customers:
        try:
            # Check if customer exists in branch
            existing = branch_client.get_doc("Customer", customer.name)
            # Update existing customer
            branch_client.update(customer)
            print(f"Updated customer {customer.name} in branch")
        except:
            # Customer doesn't exist, create new
            branch_client.insert(customer)
            print(f"Created customer {customer.name} in branch")

# Run sync
sync_customers_to_branch()</code></pre>

        <div class="use-case">
            <h3>2. Data Migration Between Systems</h3>
            <p><strong>Scenario</strong>: Migrate from legacy system to new ERPNext installation.</p>
        </div>

        <pre><code>def migrate_legacy_data():
    # Source system (old ERPNext)
    old_system = FrappeClient(
        url="https://old.company.com",
        username="<EMAIL>",
        password="old_password"
    )
    
    # Target system (new ERPNext)
    new_system = FrappeClient(
        url="https://new.company.com",
        api_key="new_api_key", 
        api_secret="new_api_secret"
    )
    
    # Migrate customers with full document structure
    old_system.migrate_doctype("Customer")
    
    # Migrate with preprocessing
    def preprocess_item(doc):
        # Clean up data before migration
        doc.pop('old_field', None)  # Remove deprecated fields
        if not doc.get('item_group'):
            doc['item_group'] = 'All Item Groups'
    
    old_system.migrate_doctype(
        "Item",
        filters={"disabled": 0},
        preprocess=preprocess_item
    )</code></pre>

        <div class="page-break"></div>

        <div class="use-case">
            <h3>3. External System Integration</h3>
            <p><strong>Scenario</strong>: Integrate ERPNext with external CRM system.</p>
        </div>

        <pre><code>class CRMIntegration:
    def __init__(self):
        self.erpnext = FrappeClient(
            url="https://erp.company.com",
            api_key="erp_api_key",
            api_secret="erp_api_secret"
        )
    
    def sync_leads_from_crm(self, crm_leads):
        """Sync leads from external CRM to ERPNext"""
        for crm_lead in crm_leads:
            lead_data = {
                "doctype": "Lead",
                "lead_name": crm_lead['name'],
                "email_id": crm_lead['email'],
                "mobile_no": crm_lead['phone'],
                "source": "CRM Integration",
                "custom_crm_id": crm_lead['id']
            }
            
            try:
                # Check if lead already exists
                existing = self.erpnext.get_doc(
                    "Lead",
                    filters={"custom_crm_id": crm_lead['id']}
                )
                
                if existing:
                    lead_data['name'] = existing['name']
                    self.erpnext.update(lead_data)
                else:
                    self.erpnext.insert(lead_data)
                    
            except Exception as e:
                print(f"Error syncing lead {crm_lead['name']}: {e}")
    
    def get_converted_leads(self):
        """Get leads converted to customers for CRM update"""
        return self.erpnext.get_list(
            "Lead",
            fields=["name", "lead_name", "custom_crm_id", "status"],
            filters={"status": "Converted"}
        )

# Usage
integration = CRMIntegration()
integration.sync_leads_from_crm(external_crm_leads)</code></pre>

        <div class="use-case">
            <h3>4. Automated Reporting & Analytics</h3>
            <p><strong>Scenario</strong>: Generate cross-system reports and analytics.</p>
        </div>

        <pre><code>def generate_multi_site_report():
    sites = [
        {"name": "HQ", "url": "https://hq.company.com"},
        {"name": "Branch1", "url": "https://branch1.company.com"},
        {"name": "Branch2", "url": "https://branch2.company.com"}
    ]
    
    consolidated_data = []
    
    for site in sites:
        client = FrappeClient(
            url=site["url"],
            api_key="shared_api_key",
            api_secret="shared_api_secret"
        )
        
        # Get sales data
        sales_data = client.get_list(
            "Sales Invoice",
            fields=["name", "customer", "grand_total", "posting_date"],
            filters={
                "posting_date": ["between", ["2024-01-01", "2024-12-31"]],
                "docstatus": 1
            }
        )
        
        for invoice in sales_data:
            invoice['site'] = site['name']
            consolidated_data.append(invoice)
    
    return consolidated_data

# Generate report
report_data = generate_multi_site_report()</code></pre>

        <div class="page-break"></div>

        <h2 id="when-to-use">When to Use FrappeClient</h2>

        <div class="best-practice">
            <h3>✅ Ideal Scenarios:</h3>
            <ol>
                <li><strong>Multi-Site Deployments</strong>
                    <ul>
                        <li>Synchronizing data between headquarters and branches</li>
                        <li>Consolidating reports from multiple sites</li>
                        <li>Managing distributed inventory</li>
                    </ul>
                </li>
                <li><strong>System Migrations</strong>
                    <ul>
                        <li>Moving from old ERPNext to new installation</li>
                        <li>Migrating from other ERP systems</li>
                        <li>Upgrading between major versions</li>
                    </ul>
                </li>
                <li><strong>Third-Party Integrations</strong>
                    <ul>
                        <li>Connecting with external CRM, accounting, or inventory systems</li>
                        <li>E-commerce platform synchronization</li>
                        <li>API-based data exchange</li>
                    </ul>
                </li>
                <li><strong>Automated Operations</strong>
                    <ul>
                        <li>Scheduled data synchronization</li>
                        <li>Bulk data processing</li>
                        <li>Automated reporting across systems</li>
                    </ul>
                </li>
                <li><strong>Development & Testing</strong>
                    <ul>
                        <li>Populating test environments with production data</li>
                        <li>Automated testing of API endpoints</li>
                        <li>Development environment setup</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="warning">
            <h3>❌ Avoid When:</h3>
            <ol>
                <li><strong>Single-Site Operations</strong>: Use direct Frappe API calls instead</li>
                <li><strong>Real-Time Synchronization</strong>: Consider webhooks or real-time APIs</li>
                <li><strong>Large Data Volumes</strong>: May hit API rate limits; consider direct database operations</li>
                <li><strong>Complex Transactions</strong>: Direct database operations might be more efficient</li>
            </ol>
        </div>

        <h2 id="best-practices">Best Practices</h2>

        <h3>1. Error Handling</h3>
        <pre><code>from frappe.frappeclient import FrappeClient, AuthError, SiteExpiredError

try:
    client = FrappeClient(url="https://site.com", api_key="key", api_secret="secret")
    result = client.get_doc("Customer", "CUST-001")
except AuthError:
    print("Authentication failed - check credentials")
except SiteExpiredError:
    print("Site subscription has expired")
except Exception as e:
    print(f"Unexpected error: {e}")</code></pre>

        <h3>2. Context Management</h3>
        <pre><code># Automatic session cleanup
with FrappeClient(url="https://site.com", username="user", password="pass") as client:
    customers = client.get_list("Customer")
    # Session automatically logged out when exiting context</code></pre>

        <h3>3. Pagination for Large Datasets</h3>
        <pre><code>def get_all_customers(client):
    all_customers = []
    limit = 100
    start = 0
    
    while True:
        batch = client.get_list(
            "Customer",
            limit_start=start,
            limit_page_length=limit
        )
        
        if not batch:
            break
            
        all_customers.extend(batch)
        start += limit
    
    return all_customers</code></pre>

        <h3>4. Rate Limiting</h3>
        <pre><code>import time

def bulk_operation_with_rate_limit(client, operations):
    for i, operation in enumerate(operations):
        try:
            # Perform operation
            result = operation(client)
            
            # Rate limiting - pause every 10 operations
            if i % 10 == 0:
                time.sleep(1)
                
        except Exception as e:
            print(f"Operation {i} failed: {e}")
            time.sleep(2)  # Longer pause on error</code></pre>

        <div class="page-break"></div>

        <h2 id="performance">Performance Considerations</h2>

        <ol>
            <li><strong>Batch Operations</strong>: Use <code>insert_many()</code> and <code>bulk_update()</code> for multiple documents</li>
            <li><strong>Field Selection</strong>: Specify only required fields to reduce payload size</li>
            <li><strong>Pagination</strong>: Use <code>limit_page_length</code> for large datasets</li>
            <li><strong>Connection Reuse</strong>: Reuse client instances instead of creating new ones</li>
            <li><strong>Error Recovery</strong>: Implement retry logic for transient failures</li>
        </ol>

        <h2 id="security">Security Best Practices</h2>

        <ol>
            <li><strong>Use API Keys</strong>: Prefer API key authentication over username/password</li>
            <li><strong>HTTPS Only</strong>: Always use HTTPS URLs for production</li>
            <li><strong>Credential Management</strong>: Store credentials securely (environment variables, key vaults)</li>
            <li><strong>Permission Validation</strong>: Ensure API user has minimal required permissions</li>
            <li><strong>SSL Verification</strong>: Keep <code>verify=True</code> unless in development environments</li>
        </ol>

        <div class="best-practice">
            <h3>Conclusion</h3>
            <p>FrappeClient is a powerful tool for integrating Frappe systems, enabling seamless data exchange and automation across distributed deployments. Choose the appropriate authentication method and implement proper error handling for robust integrations.</p>
        </div>

        <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #6c757d;">
            <p>FrappeClient Documentation - Generated on <?php echo date('Y-m-d'); ?></p>
        </footer>
    </div>
</body>
</html>
