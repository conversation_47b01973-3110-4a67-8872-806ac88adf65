# VFD Providers - Hooks Configuration Documentation

## Overview

This document provides comprehensive information about the hooks configuration in the VFD Providers application. Hooks are integration points that allow the app to interact with the Frappe framework and extend functionality of existing DocTypes.

## Hooks File Location
**File:** `apps/vfd_providers/vfd_providers/hooks.py`

## Application Metadata

### Basic Information
```python
app_name = "vfd_providers"
app_title = "VFD Providers"
app_publisher = "Aakvatech Limited"
app_description = "VFD Providers"
app_email = "<EMAIL>"
app_license = "GPL"
```

**Purpose:** Defines the application identity and metadata used by the Frappe framework.

## Client-Side Integrations

### DocType JavaScript Files
```python
doctype_js = {
    "Sales Invoice": "utils/sales_invoice.js",
    "Customer": "utils/customer.js",
}
```

#### Sales Invoice JavaScript
**File:** `apps/vfd_providers/vfd_providers/utils/sales_invoice.js`
**Purpose:** Enhances Sales Invoice form with VFD-specific functionality

**Key Features:**
- VFD generation button handling
- Form field validations
- Customer ID type auto-population
- VFD status display enhancements

#### Customer JavaScript
**File:** `apps/vfd_providers/vfd_providers/utils/customer.js`
**Purpose:** Enhances Customer form with VFD-specific functionality

**Key Features:**
- VFD customer ID validation
- Tax ID formatting
- Customer ID type selection logic

## Server-Side Event Hooks

### Document Events Configuration
```python
doc_events = {
    "Sales Invoice": {
        "on_submit": "vfd_providers.utils.utils.autogenerate_vfd",
        "before_cancel": "vfd_providers.utils.sales_invoice.validate_cancel",
        "before_submit": "vfd_providers.utils.sales_invoice.vfd_validation",
    },
    "Customer": {
        "validate": "vfd_providers.utils.utils.clean_and_update_tax_id_info",
    },
}
```

### Sales Invoice Events

#### 1. on_submit Event
**Function:** `vfd_providers.utils.utils.autogenerate_vfd`
**Trigger:** After Sales Invoice is successfully submitted
**Purpose:** Automatically generates VFD receipt if auto-generation is enabled

**Business Logic:**
- Checks if invoice is eligible for VFD processing
- Verifies auto-generation flag is enabled
- Calls appropriate VFD provider API
- Updates invoice with VFD response data

#### 2. before_cancel Event
**Function:** `vfd_providers.utils.sales_invoice.validate_cancel`
**Trigger:** Before Sales Invoice cancellation is processed
**Purpose:** Prevents cancellation of invoices already sent to TRA

**Validation Rules:**
- Checks if VFD receipt number (vfd_rctvnum) exists
- Throws error if invoice is already registered with TRA
- Ensures compliance with tax authority requirements

#### 3. before_submit Event
**Function:** `vfd_providers.utils.sales_invoice.vfd_validation`
**Trigger:** Before Sales Invoice submission is processed
**Purpose:** Validates VFD-related data and requirements

**Validation Checks:**
- Ensures base net total is not zero
- Validates tax configuration
- Checks customer VFD ID information
- Verifies VFD provider settings

### Customer Events

#### 1. validate Event
**Function:** `vfd_providers.utils.utils.clean_and_update_tax_id_info`
**Trigger:** During Customer document validation
**Purpose:** Cleans and standardizes tax ID information for VFD compliance

**Processing Logic:**
- Removes non-numeric characters from tax ID
- Sets VFD customer ID type based on tax ID presence
- Assigns default customer ID for customers without tax ID
- Ensures VFD compliance for all customer records

## Scheduled Tasks

### Cron Job Configuration
```python
scheduler_events = {
    "cron": {
        "*/10 * * * *": [
            "vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token"
        ],
        "*/15 * * * *": [
            "vfd_providers.utils.utils.posting_all_vfd_invoices",
        ],
    }
}
```

### Token Refresh Task
**Schedule:** Every 10 minutes (`*/10 * * * *`)
**Function:** `simplify_vfd_settings.get_token`
**Purpose:** Automatically refreshes authentication tokens for SimplifyVFD provider

**Functionality:**
- Checks token expiration for all SimplifyVFD settings
- Refreshes expired tokens automatically
- Maintains continuous API connectivity
- Prevents authentication failures during VFD posting

### VFD Invoice Posting Task
**Schedule:** Every 15 minutes (`*/15 * * * *`)
**Function:** `vfd_providers.utils.utils.posting_all_vfd_invoices`
**Purpose:** Automatically posts pending VFD invoices to tax authorities

**Processing Logic:**
- Scans all companies for pending VFD invoices
- Identifies invoices with "Pending" or "Failed" status
- Attempts to post invoices to configured VFD provider
- Updates invoice status based on posting results
- Handles multiple VFD providers (VFDPlus, TotalVFD, SimplifyVFD)

## Integration Points

### VFD Provider Support
The hooks system supports multiple VFD providers:

1. **VFDPlus:** Legacy VFD provider integration
2. **TotalVFD:** Modern VFD provider with enhanced features
3. **SimplifyVFD:** Simplified VFD provider with token-based authentication

### Error Handling
- Graceful error handling in all hook functions
- Logging of VFD posting attempts and failures
- Prevention of duplicate VFD submissions
- Rollback mechanisms for failed operations

## Configuration Management

### Environment-Specific Settings
- Development vs Production API endpoints
- Sandbox mode support for testing
- Company-specific VFD provider selection
- Token management and refresh strategies

### Security Considerations
- Secure token storage using Frappe's password fields
- API key protection and encryption
- Audit trail for VFD submissions
- Compliance with data protection requirements

## Troubleshooting

### Common Issues
1. **Token Expiration:** Resolved by automatic token refresh
2. **Network Connectivity:** Retry mechanisms in place
3. **Invalid Customer Data:** Validation prevents submission
4. **Duplicate Submissions:** Status checking prevents duplicates

### Monitoring
- Scheduled task execution logs
- VFD posting success/failure rates
- Token refresh frequency and success
- Invoice processing queue status

## Related Documentation
- [VFD Providers Utility Modules Documentation](VFD_Providers_Utility_Modules_Documentation.md)
- [VFD Providers DocTypes Documentation](VFD_Providers_DocTypes_Documentation.md)
- [VFD Providers Patch Files Documentation](VFD_Providers_Patch_Files_Documentation.md)

## Technical References
- [Frappe Hooks Documentation](https://frappeframework.com/docs/user/en/python-api/hooks)
- [Document Events Guide](https://frappeframework.com/docs/user/en/python-api/document-events)
- [Scheduler Events Documentation](https://frappeframework.com/docs/user/en/python-api/scheduler-events)