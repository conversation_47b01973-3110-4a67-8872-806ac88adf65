# Supplier Ledger Summary Report - Technical Documentation

## Table of Contents
1. [Overview](#overview)
2. [Report Architecture](#report-architecture)
3. [Data Sources](#data-sources)
4. [Data Processing Logic](#data-processing-logic)
5. [Column Definitions](#column-definitions)
6. [Filtering Mechanism](#filtering-mechanism)
7. [Performance Considerations](#performance-considerations)
8. [Implementation Details](#implementation-details)
9. [Troubleshooting Guide](#troubleshooting-guide)

---

## Overview

The Supplier Ledger Summary report is a comprehensive financial report that provides a consolidated view of all supplier transactions and outstanding balances. It aggregates data from multiple sources to present opening balances, transaction summaries, and closing balances for suppliers within a specified date range.

### Report Classification
- **Type**: Script Report
- **Module**: Accounts
- **Primary Reference DocType**: Purchase Invoice
- **Base Class**: PartyLedgerSummaryReport (shared with Customer Ledger Summary)

---

## Report Architecture

### Class Structure
```python
class PartyLedgerSummaryReport:
    def __init__(self, filters=None)
    def run(self, args)
    def validate_filters()
    def get_party_details()
    def get_gl_entries()
    def get_return_invoices()
    def get_party_adjustment_amounts()
    def get_data()
```

### Execution Flow
```python
def execute(filters=None):
    args = {
        "party_type": "Supplier",
        "naming_by": ["Buying Settings", "supp_master_name"],
    }
    return PartyLedgerSummaryReport(filters).run(args)
```

The report inherits from `PartyLedgerSummaryReport` class, which is a shared implementation used by both Customer and Supplier ledger summary reports. The key difference is the `party_type` parameter set to "Supplier".

---

## Data Sources

### Primary Data Source: GL Entry (General Ledger)

The report's primary data source is the `GL Entry` doctype, which contains all accounting transactions in the system.

#### Key GL Entry Fields Used:
| Field | Purpose | Data Type |
|-------|---------|-----------|
| `posting_date` | Transaction date for period filtering | Date |
| `party` | Supplier identifier | Dynamic Link |
| `party_type` | Must be "Supplier" | Link |
| `voucher_type` | Document type (Purchase Invoice, Payment Entry, etc.) | Link |
| `voucher_no` | Document number/identifier | Dynamic Link |
| `against_voucher` | Reference to original invoice for payments | Dynamic Link |
| `against_voucher_type` | Type of referenced document | Link |
| `debit` | Debit amount in company currency | Currency |
| `credit` | Credit amount in company currency | Currency |
| `is_opening` | Identifies opening balance entries | Literal |
| `is_cancelled` | Excludes cancelled entries | Check |
| `docstatus` | Document status (excludes draft/cancelled) | Int |
| `company` | Company filter | Link |
| `cost_center` | Cost center dimension | Link |
| `project` | Project dimension | Link |
| `finance_book` | Finance book for multi-book accounting | Link |

#### GL Entry Query Logic:
```sql
SELECT posting_date, party, voucher_type, voucher_no, against_voucher, 
       debit, credit, is_opening
FROM `tabGL Entry`
WHERE docstatus < 2 
  AND is_cancelled = 0 
  AND party_type = 'Supplier'
  AND party IS NOT NULL AND party != ''
  AND posting_date <= %(to_date)s
  AND party IN (selected_suppliers)
  AND company = %(company)s
```

### Secondary Data Sources

#### 1. Supplier Master Data
- **Source**: `Supplier` doctype
- **Purpose**: Supplier details and filtering
- **Fields Used**: `name`, `supplier_name`, `supplier_group`, `territory`, `payment_terms`

#### 2. Purchase Invoice (Return Invoices)
- **Source**: `Purchase Invoice` doctype with `is_return = 1`
- **Purpose**: Identify debit notes and return transactions
- **Query**: 
```sql
SELECT name FROM `tabPurchase Invoice`
WHERE is_return = 1 
  AND docstatus = 1
  AND posting_date BETWEEN %(from_date)s AND %(to_date)s
  AND supplier IN (selected_suppliers)
```

#### 3. Buying Settings
- **Source**: `Buying Settings` single doctype
- **Purpose**: Supplier naming convention
- **Field**: `supp_master_name`

#### 4. Account Master
- **Source**: `Account` doctype
- **Purpose**: Account type validation for adjustments
- **Usage**: Identifies Income Account type for adjustment calculations

---

## Data Processing Logic

### 1. Supplier Selection Process

The system first identifies which suppliers to include based on filters and user permissions:

```python
def get_party_details(self):
    conditions = self.get_party_conditions(doctype)
    query = (
        qb.from_(doctype)
        .select(doctype.name.as_("party"), f"{scrub(party_type)}_name")
        .where(Criterion.all(conditions))
    )
    
    # Apply user permissions
    match_conditions = build_match_conditions(party_type)
    if match_conditions:
        query = query.where(LiteralValue(match_conditions))
```

### 2. Balance Calculation Logic

For suppliers, the system uses **credit-based accounting** where:
- **Credit entries** increase supplier liability (money owed to supplier)
- **Debit entries** decrease supplier liability (payments made)

```python
# For suppliers: invoice_dr_or_cr = "credit", reverse_dr_or_cr = "debit"
amount = gle.get("credit") - gle.get("debit")
```

#### Amount Interpretation:
- **Positive amount**: Increases what you owe the supplier
- **Negative amount**: Decreases what you owe the supplier

### 3. Transaction Classification

#### Opening Balance Calculation:
```python
if gle.posting_date < self.filters.from_date or gle.is_opening == "Yes":
    self.party_data[gle.party].opening_balance += amount
```

#### Current Period Transaction Classification:
```python
else:
    # Check if this is a direct return invoice
    if gle.voucher_no in self.return_invoices:
        party_data.return_amount -= amount
    # Check if this entry is against a return invoice
    elif gle.against_voucher in self.return_invoices:
        if amount > 0:
            party_data.paid_amount -= amount
        else:
            party_data.invoiced_amount += amount
    # Normal transaction logic
    else:
        if amount > 0:
            party_data.invoiced_amount += amount
        else:
            party_data.paid_amount -= amount
```

### 4. Return Invoice Handling

The system has sophisticated logic for handling return invoices (debit notes):

#### Direct Return Invoices:
- Purchase invoices marked with `is_return = 1`
- These reduce the return_amount (shown as Debit Note column)

#### Payments Against Return Invoices:
- GL entries where `against_voucher` references a return invoice
- Positive amounts are treated as payments
- Negative amounts are treated as invoice adjustments

### 5. Adjustment Calculation

The system identifies adjustment entries by analyzing GL entries without party information but linked to the same vouchers:

```python
def get_party_adjustment_amounts(self):
    # Find GL entries without party but same voucher
    # Identify Income Account type entries
    # Match with party entries in same voucher
    # Calculate adjustment amounts per account
```

---

## Column Definitions

### Standard Columns

| Column | Calculation | Business Meaning |
|--------|-------------|------------------|
| **Supplier** | Direct from party field | Supplier identifier/name |
| **Supplier Name** | From supplier master | Display name (if naming by series) |
| **Opening Balance** | Sum of amounts before from_date + opening entries | Balance at start of period |
| **Invoiced Amount** | Sum of positive amounts in period (excluding returns) | New purchases/invoices |
| **Paid Amount** | Sum of negative amounts in period (excluding returns) | Payments made to supplier |
| **Debit Note** | Sum of return invoice amounts | Returns/credit adjustments |
| **Closing Balance** | Opening + Invoiced - Paid - Returns + Adjustments | Final balance owed |
| **Currency** | Company default currency | Reporting currency |

### Dynamic Adjustment Columns

For each identified adjustment account, the system creates additional columns:
- **Column Name**: `adj_` + account_name (scrubbed)
- **Purpose**: Show specific adjustment amounts by account type
- **Calculation**: Sum of adjustment amounts for that account

### Hidden Columns (for User Permissions)

- **Supplier Group**: For permission-based filtering
- **Territory**: For regional access control

---

## Filtering Mechanism

### Required Filters
- **Company**: Mandatory for multi-company setups
- **From Date**: Start of analysis period
- **To Date**: End of analysis period

### Optional Filters

#### Supplier-Specific Filters:
- **Supplier**: Focus on specific supplier
- **Supplier Group**: Filter by supplier category
- **Payment Terms Template**: Filter by payment terms

#### Accounting Filters:
- **Finance Book**: For multi-book accounting
- **Cost Center**: Departmental filtering
- **Project**: Project-based filtering

#### Hierarchical Filters:
The system supports tree-structured filters that automatically include child nodes:
```python
def update_hierarchical_filters(self):
    for doctype in TREE_DOCTYPES:
        if self.filters.get(key):
            self.filters[key] = get_children(doctype, self.filters[key])
```

### User Permission Integration

The report respects user permissions through Frappe's `build_match_conditions()` function, ensuring users only see suppliers they have access to.

---

## Performance Considerations

### Query Optimization

1. **Indexed Fields**: The system uses indexed fields for filtering:
   - `posting_date` (search_index: 1)
   - `party_type` (search_index: 1)
   - `party` (search_index: 1)
   - `account` (search_index: 1)

2. **Efficient Filtering**: 
   - Date range filtering applied at database level
   - Party type filtering reduces dataset significantly
   - Company filtering for multi-company setups

3. **Query Builder Usage**: 
   - Uses Frappe's Query Builder for optimized SQL generation
   - Avoids N+1 query problems through batch processing

### Memory Management

1. **Batch Processing**: Processes GL entries in batches
2. **Selective Field Loading**: Only loads required fields from database
3. **Cached Lookups**: Caches party details to avoid repeated queries

### Scalability Considerations

1. **Large Datasets**: For companies with millions of GL entries, consider:
   - Date range limitations
   - Supplier-specific filtering
   - Indexed custom fields for frequent filters

2. **Multi-Company**: Efficient company-based partitioning
3. **Multi-Currency**: Handles currency conversion efficiently

---

## Implementation Details

### File Structure
```
apps/erpnext/erpnext/accounts/report/supplier_ledger_summary/
├── __init__.py
├── supplier_ledger_summary.py      # Main execution logic
├── supplier_ledger_summary.js      # Frontend filters and logic
├── supplier_ledger_summary.json    # Report configuration
└── test_supplier_ledger_summary.py # Unit tests
```

### Key Configuration (JSON)
```json
{
 "add_total_row": 1,
 "is_standard": "Yes",
 "module": "Accounts",
 "ref_doctype": "Purchase Invoice",
 "report_type": "Script Report",
 "roles": [
  {"role": "Accounts Manager"},
  {"role": "Accounts User"}
 ]
}
```

### Frontend Integration (JavaScript)
The JavaScript file defines:
- Filter definitions and defaults
- Dynamic filter interactions
- Supplier name auto-population
- Tax ID integration

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Missing Suppliers in Report
**Symptoms**: Expected suppliers not appearing
**Possible Causes**:
- No GL entries in selected date range
- User permission restrictions
- Inactive supplier records
- Incorrect party_type in GL entries

**Solution**: 
```sql
-- Check GL entries for supplier
SELECT * FROM `tabGL Entry` 
WHERE party = 'SUPPLIER-ID' 
  AND party_type = 'Supplier'
  AND posting_date BETWEEN 'from_date' AND 'to_date'
```

#### 2. Incorrect Balances
**Symptoms**: Balances don't match expectations
**Possible Causes**:
- Missing or duplicate GL entries
- Incorrect opening balance entries
- Currency conversion issues
- Cancelled entries not properly excluded

**Solution**:
```sql
-- Verify GL entry totals
SELECT party, SUM(credit - debit) as balance
FROM `tabGL Entry`
WHERE party_type = 'Supplier' 
  AND party = 'SUPPLIER-ID'
  AND docstatus < 2 
  AND is_cancelled = 0
```

#### 3. Performance Issues
**Symptoms**: Report takes too long to load
**Possible Causes**:
- Large date ranges
- Missing database indexes
- Too many suppliers selected

**Solutions**:
- Limit date ranges
- Use specific supplier filters
- Ensure proper indexing on GL Entry table

#### 4. Return Invoice Misclassification
**Symptoms**: Returns showing in wrong columns
**Possible Causes**:
- Purchase invoices not marked as returns
- Incorrect against_voucher references
- Missing return invoice identification

**Solution**:
```sql
-- Verify return invoice setup
SELECT name, is_return, return_against 
FROM `tabPurchase Invoice`
WHERE supplier = 'SUPPLIER-ID' 
  AND is_return = 1
```

### Data Validation Queries

#### Verify GL Entry Completeness:
```sql
SELECT 
    voucher_type,
    COUNT(*) as entry_count,
    SUM(debit) as total_debit,
    SUM(credit) as total_credit
FROM `tabGL Entry`
WHERE party_type = 'Supplier'
  AND posting_date BETWEEN %(from_date)s AND %(to_date)s
GROUP BY voucher_type
```

#### Check Opening Balance Entries:
```sql
SELECT party, SUM(credit - debit) as opening_balance
FROM `tabGL Entry`
WHERE party_type = 'Supplier'
  AND (posting_date < %(from_date)s OR is_opening = 'Yes')
  AND docstatus < 2
  AND is_cancelled = 0
GROUP BY party
```

---

## Conclusion

The Supplier Ledger Summary report is a sophisticated financial reporting tool that aggregates data from multiple sources to provide comprehensive supplier balance information. Understanding its data sources, processing logic, and implementation details is crucial for:

1. **Accurate Financial Reporting**: Ensuring data integrity and completeness
2. **Performance Optimization**: Implementing efficient filtering and querying
3. **Troubleshooting**: Diagnosing and resolving data discrepancies
4. **Customization**: Extending functionality for specific business requirements

The report's strength lies in its comprehensive data aggregation from the GL Entry table, sophisticated handling of return invoices and adjustments, and flexible filtering capabilities that respect user permissions and organizational hierarchies.

---

## Appendix A: Database Schema Reference

### GL Entry Table Structure
```sql
CREATE TABLE `tabGL Entry` (
  `name` varchar(140) NOT NULL,
  `posting_date` date DEFAULT NULL,
  `transaction_date` date DEFAULT NULL,
  `account` varchar(140) DEFAULT NULL,
  `party_type` varchar(140) DEFAULT NULL,
  `party` varchar(140) DEFAULT NULL,
  `cost_center` varchar(140) DEFAULT NULL,
  `debit` decimal(18,6) NOT NULL DEFAULT 0.000000,
  `credit` decimal(18,6) NOT NULL DEFAULT 0.000000,
  `voucher_type` varchar(140) DEFAULT NULL,
  `voucher_no` varchar(140) DEFAULT NULL,
  `against_voucher_type` varchar(140) DEFAULT NULL,
  `against_voucher` varchar(140) DEFAULT NULL,
  `is_opening` varchar(140) DEFAULT 'No',
  `is_cancelled` int(1) NOT NULL DEFAULT 0,
  `docstatus` int(1) NOT NULL DEFAULT 0,
  `company` varchar(140) DEFAULT NULL,
  `finance_book` varchar(140) DEFAULT NULL,
  PRIMARY KEY (`name`),
  KEY `posting_date` (`posting_date`),
  KEY `party_type` (`party_type`),
  KEY `party` (`party`),
  KEY `account` (`account`),
  KEY `voucher_no` (`voucher_no`)
);
```

### Supplier Table Structure
```sql
CREATE TABLE `tabSupplier` (
  `name` varchar(140) NOT NULL,
  `supplier_name` varchar(140) DEFAULT NULL,
  `supplier_group` varchar(140) DEFAULT NULL,
  `territory` varchar(140) DEFAULT NULL,
  `payment_terms` varchar(140) DEFAULT NULL,
  `tax_id` varchar(140) DEFAULT NULL,
  `disabled` int(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`name`),
  KEY `supplier_group` (`supplier_group`),
  KEY `territory` (`territory`)
);
```

---

## Appendix B: Sample Data Flow

### Example Transaction Flow
```
1. Purchase Invoice Created:
   - GL Entry: Credit Supplier Account (+$1000)
   - GL Entry: Debit Expense Account (+$1000)

2. Payment Made:
   - GL Entry: Debit Supplier Account (+$800)
   - GL Entry: Credit Bank Account (+$800)

3. Return/Debit Note:
   - GL Entry: Debit Supplier Account (+$100)
   - GL Entry: Credit Expense Account (+$100)

4. Report Calculation:
   - Opening Balance: $0
   - Invoiced Amount: $1000 (from step 1)
   - Paid Amount: $800 (from step 2)
   - Debit Note: $100 (from step 3)
   - Closing Balance: $1000 - $800 - $100 = $100
```

---

## Appendix C: API Reference

### Main Functions

#### execute(filters=None)
**Purpose**: Main entry point for report execution
**Parameters**:
- `filters` (dict): Report filter parameters
**Returns**: Tuple of (columns, data)

#### PartyLedgerSummaryReport.run(args)
**Purpose**: Core report processing logic
**Parameters**:
- `args` (dict): Configuration parameters including party_type
**Returns**: Tuple of (columns, data)

#### get_gl_entries()
**Purpose**: Retrieves GL entries for selected suppliers
**Query Conditions**:
- `docstatus < 2` (exclude cancelled documents)
- `is_cancelled = 0` (exclude cancelled entries)
- `party_type = 'Supplier'`
- `posting_date <= to_date`

#### get_return_invoices()
**Purpose**: Identifies return invoices for proper classification
**Query**: Purchase Invoices with `is_return = 1`

#### get_party_adjustment_amounts()
**Purpose**: Calculates adjustment amounts from expense accounts
**Logic**: Matches GL entries without party to vouchers with party entries

---

## Appendix D: Customization Guidelines

### Adding Custom Filters

To add custom filters, modify the JavaScript file:

```javascript
// In supplier_ledger_summary.js
{
    fieldname: "custom_field",
    label: __("Custom Field"),
    fieldtype: "Link",
    options: "Custom DocType"
}
```

Then update the Python query conditions:

```python
# In customer_ledger_summary.py - prepare_conditions method
if self.filters.custom_field:
    query = query.where(gle.custom_field == self.filters.custom_field)
```

### Adding Custom Columns

To add custom columns, extend the get_columns method:

```python
def get_columns(self):
    columns = super().get_columns()
    columns.append({
        "label": _("Custom Column"),
        "fieldname": "custom_column",
        "fieldtype": "Currency",
        "width": 120
    })
    return columns
```

### Performance Optimization Tips

1. **Add Database Indexes**:
```sql
ALTER TABLE `tabGL Entry` ADD INDEX `idx_party_date` (`party_type`, `party`, `posting_date`);
```

2. **Optimize Date Range Queries**:
```python
# Use smaller date ranges for better performance
if date_diff(self.filters.to_date, self.filters.from_date) > 365:
    frappe.msgprint("Consider using smaller date ranges for better performance")
```

3. **Implement Caching**:
```python
# Cache frequently accessed data
@frappe.cache()
def get_supplier_details(supplier):
    return frappe.get_doc("Supplier", supplier)
```

---

## Appendix E: Integration Points

### Related Reports
- **Accounts Payable Summary**: Aggregated view of all payables
- **Purchase Register**: Detailed purchase transaction listing
- **Payment Entry Report**: Payment transaction details
- **General Ledger**: Complete accounting entry details

### Workflow Integration
- **Purchase Invoice Workflow**: Affects invoiced amounts
- **Payment Entry Workflow**: Affects paid amounts
- **Purchase Return Workflow**: Affects return amounts

### API Endpoints
- **GET /api/method/frappe.desk.query_report.run**: Execute report via API
- **POST /api/method/frappe.desk.query_report.export_query**: Export report data

---

## Document Information

**Document Version**: 1.0
**Last Updated**: January 2025
**Author**: Technical Documentation Team
**Review Status**: Technical Review Complete
**Target Audience**: Developers, System Administrators, Business Analysts

**Change Log**:
- v1.0: Initial comprehensive documentation covering all technical aspects

**Related Documentation**:
- ERPNext Accounts Module Documentation
- Frappe Framework Query Builder Guide
- General Ledger Implementation Guide
