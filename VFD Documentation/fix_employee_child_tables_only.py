#!/usr/bin/env python3
"""
Script to fix only Employee-related child tables missing parent fields.
This script will revert non-Employee child tables and only fix Employee-related ones.
"""

import os
import json
import subprocess
import frappe


def get_employee_related_child_tables():
    """Get only Employee-related child tables"""
    employee_child_tables = [
        {
            'name': 'Employee Salary Component Limit',
            'path': 'apps/csf_tz/csf_tz/csf_tz/doctype/employee_salary_component_limit/employee_salary_component_limit.json'
        },
        {
            'name': 'Employee OT Component', 
            'path': 'apps/csf_tz/csf_tz/csf_tz/doctype/employee_ot_component/employee_ot_component.json'
        },
        {
            'name': 'Employee Piecework Additional Salary',
            'path': 'apps/csf_tz/csf_tz/csf_tz/doctype/employee_piecework_additional_salary/employee_piecework_additional_salary.json'
        },
        {
            'name': 'Single Piecework Employees',
            'path': 'apps/csf_tz/csf_tz/csf_tz/doctype/single_piecework_employees/single_piecework_employees.json'
        },
        {
            'name': 'Email Employee Salary Slip',
            'path': 'apps/csf_tz/csf_tz/csf_tz/doctype/email_employee_salary_slip/email_employee_salary_slip.json'
        },
        {
            'name': 'Salary Slip OT Component',
            'path': 'apps/csf_tz/csf_tz/csf_tz/doctype/salary_slip_ot_component/salary_slip_ot_component.json'
        }
    ]
    return employee_child_tables


def revert_git_changes():
    """Revert all changes to get back to original state"""
    print("Reverting all changes to start fresh...")
    try:
        subprocess.run(['git', 'checkout', '.'], cwd='/home/<USER>/Desktop/frappe-bench', check=True)
        print("✓ Successfully reverted all changes")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to revert changes: {e}")
        return False


def add_parent_fields_to_doctype(doctype_data):
    """Add missing parent fields to a child table doctype"""
    required_fields = ['parent', 'parentfield', 'parenttype']
    existing_fields = [field.get('fieldname') for field in doctype_data.get('fields', [])]
    missing_fields = [field for field in required_fields if field not in existing_fields]
    
    if not missing_fields:
        return False, "No missing fields"
    
    # Standard parent fields for child tables
    parent_fields = [
        {
            "fieldname": "parent",
            "fieldtype": "Data",
            "hidden": 1,
            "label": "Parent",
            "no_copy": 1,
            "print_hide": 1,
            "read_only": 1
        },
        {
            "fieldname": "parentfield",
            "fieldtype": "Data", 
            "hidden": 1,
            "label": "Parent Field",
            "no_copy": 1,
            "print_hide": 1,
            "read_only": 1
        },
        {
            "fieldname": "parenttype",
            "fieldtype": "Data",
            "hidden": 1,
            "label": "Parent Type",
            "no_copy": 1,
            "print_hide": 1,
            "read_only": 1
        }
    ]
    
    # Add missing fields to field_order and fields
    for field in parent_fields:
        if field['fieldname'] in missing_fields:
            # Add to field_order
            if 'field_order' not in doctype_data:
                doctype_data['field_order'] = []
            doctype_data['field_order'].append(field['fieldname'])
            
            # Add to fields
            if 'fields' not in doctype_data:
                doctype_data['fields'] = []
            doctype_data['fields'].append(field)
    
    return True, f"Added fields: {', '.join(missing_fields)}"


def fix_employee_child_table(table_info):
    """Fix a single Employee-related child table"""
    name = table_info['name']
    path = table_info['path']
    
    print(f"\nProcessing: {name}")
    print(f"Path: {path}")
    
    # Check if file exists
    if not os.path.exists(path):
        print(f"✗ {name}: File not found")
        return False
    
    # Load the JSON file
    try:
        with open(path, 'r') as f:
            doctype_data = json.load(f)
    except Exception as e:
        print(f"✗ {name}: Failed to read file - {str(e)}")
        return False
    
    # Check if it's actually a child table
    if not doctype_data.get('istable'):
        print(f"✗ {name}: Not a child table")
        return False
    
    # Check what's missing
    required_fields = ['parent', 'parentfield', 'parenttype']
    existing_fields = [field.get('fieldname') for field in doctype_data.get('fields', [])]
    missing_fields = [field for field in required_fields if field not in existing_fields]
    
    if not missing_fields:
        print(f"✓ {name} already has all required parent fields")
        return True
    
    print(f"Missing fields: {', '.join(missing_fields)}")
    
    # Add missing fields
    modified, message = add_parent_fields_to_doctype(doctype_data)
    if not modified:
        print(f"✗ {name}: {message}")
        return False
    
    # Write back to file
    try:
        with open(path, 'w') as f:
            json.dump(doctype_data, f, indent=1, separators=(',', ': '))
        print(f"✓ {name}: {message}")
        return True
    except Exception as e:
        print(f"✗ {name}: Failed to write file - {str(e)}")
        return False


def main():
    """Main function to fix only Employee-related child tables"""
    print("=== Fixing Employee-Related Child Tables Only ===")
    
    # First revert all changes
    if not revert_git_changes():
        print("Failed to revert changes. Please manually reset the repository.")
        return
    
    # Get Employee-related child tables
    employee_tables = get_employee_related_child_tables()
    print(f"\nFound {len(employee_tables)} Employee-related child tables to fix")
    
    # Process each Employee-related child table
    fixed_count = 0
    error_count = 0
    
    for table in employee_tables:
        try:
            if fix_employee_child_table(table):
                fixed_count += 1
            else:
                error_count += 1
        except Exception as e:
            print(f"✗ {table['name']}: Unexpected error - {str(e)}")
            error_count += 1
    
    print(f"\n=== Summary ===")
    print(f"Employee-related child tables: {len(employee_tables)}")
    print(f"Successfully processed: {fixed_count}")
    print(f"Errors: {error_count}")
    
    if fixed_count > 0:
        print(f"\n⚠️  IMPORTANT: Run 'bench migrate' to apply database schema changes")
        print(f"📝 This focused fix only addresses Employee-related child tables for a clean PR")


if __name__ == "__main__":
    main()
