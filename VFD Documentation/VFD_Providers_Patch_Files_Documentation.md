# VFD Providers - Patch Files Documentation

## Overview

This document provides comprehensive information about all patch files in the VFD Providers application. Patch files are used to migrate data, create custom fields, and update system configurations during application updates.

## Patch Configuration

### Patches.txt File
**Location:** `apps/vfd_providers/vfd_providers/patches.txt`

The patches.txt file defines the execution order of patches:

```ini
[pre_model_sync]

[post_model_sync]
vfd_providers.patches.custom_fields.vfd_providers_updated_custom_fields
```

**Purpose:** Controls when patches are executed during the application installation/update process.

## Patch Files Inventory

### 1. Custom Fields Patch

**File:** `vfd_providers_updated_custom_fields.py`
**Location:** `apps/vfd_providers/vfd_providers/patches/custom_fields/`
**Type:** Custom Fields Creation
**Execution:** Post Model Sync

#### Purpose
Creates custom fields required for VFD (Virtual Fiscal Device) functionality across multiple DocTypes.

#### Target DocTypes and Fields

| DocType | Field Name | Field Type | Purpose |
|---------|------------|------------|---------|
| **Customer** | vfd_details | Section Break | Groups VFD-related customer information |
| | vfd_cust_id | Data | Stores customer's VFD identification number |
| | vfd_cust_id_type | Select | Defines the type of customer ID (TIN, Passport, etc.) |
| **Sales Invoice** | generate_vfd | Button | Triggers VFD generation process |
| | vfd_details | Section Break | Groups VFD-related invoice information |
| | vfd_date | Date | Records VFD submission date |
| | vfd_time | Time | Records VFD submission time |
| | vfd_posting_info | Link | Links to VFD Provider Posting record |
| | vfd_verification_url | Data | Stores TRA verification URL |
| | vfd_dc | Int | VFD Document Counter |
| | vfd_gc | Int | VFD Global Counter |
| | vfd_status | Select | Tracks VFD submission status |
| | is_not_vfd_invoice | Check | Excludes invoice from VFD processing |
| | is_auto_generate_vfd | Check | Enables automatic VFD generation |
| | vfd_cust_id_type | Data | Customer ID type (fetched from Customer) |
| | vfd_cust_id | Data | Customer ID (fetched from Customer) |
| | vfd_rctnum | Data | VFD Receipt Number |
| | vfd_rctvnum | Data | VFD Receipt Verification Number |
| | vfd_serial | Data | VFD Serial Number |
| **Item Tax Template** | vfd_taxcode | Select | Defines VFD tax classification |
| **Mode of Payment** | vfd_pmttype | Select | Defines VFD payment type |

#### VFD Customer ID Types
- 1- TIN (Tax Identification Number)
- 2- Passport
- 3- Driving License
- 4- Voter ID
- 5- Aadhaar
- 6- Other

#### VFD Tax Codes
- 1- Standard Rate (18%)
- 2- Special Rate
- 3- Zero rated
- 4- Special Relief
- 5- Exempt

#### VFD Payment Types
- CASH
- CHEQUE
- CCARD (Credit Card)
- EMONEY (Electronic Money)
- INVOICE

### 2. Data Fix Patch

**File:** `fixing_messed_up_data_created_by_vfd_with_no_copy_patch.py`
**Location:** `apps/vfd_providers/vfd_providers/patches/`
**Type:** Data Correction
**Execution:** Manual/On-demand

#### Purpose
Fixes data inconsistencies created by previous VFD implementations.

#### Actions Performed
1. **Updates Custom Field Options:** Sets the correct options for `Sales Invoice-vfd_posting_info` field to link to "VFD Provider Posting"
2. **Removes Obsolete Field:** Deletes the deprecated `Sales Invoice-vfd_z_report` custom field

#### Business Impact
- Ensures proper linking between Sales Invoices and VFD Provider Posting records
- Removes unused fields that could cause confusion
- Maintains data integrity in VFD-related transactions

## Patch Execution Process

### Pre-Model Sync Patches
Currently no patches are configured to run before model synchronization.

### Post-Model Sync Patches
1. **Custom Fields Creation:** Adds all VFD-related custom fields to existing DocTypes
2. **Data Fixes:** Corrects any data inconsistencies from previous versions

## Technical Implementation

### Custom Field Creation Process
```python
def execute():
    fields = {
        "DocType_Name": [
            {
                "fieldname": "field_name",
                "label": "Field Label",
                "fieldtype": "Field_Type",
                "insert_after": "existing_field",
                # Additional field properties
            }
        ]
    }
    create_custom_fields(fields, update=True)
```

### Field Properties Used
- **no_copy:** Prevents field from being copied when duplicating documents
- **allow_on_submit:** Allows field modification after document submission
- **read_only:** Makes field non-editable
- **reqd:** Makes field mandatory
- **translatable:** Enables field translation
- **depends_on:** Shows field conditionally based on other field values
- **fetch_from:** Automatically fetches value from linked document
- **fetch_if_empty:** Only fetches if field is empty

## Maintenance and Updates

### Adding New Patches
1. Create patch file in appropriate subdirectory
2. Add patch path to `patches.txt` under correct section
3. Implement `execute()` function with required logic
4. Test patch in development environment

### Best Practices
- Always backup data before running patches
- Test patches in staging environment first
- Use descriptive patch file names
- Document patch purpose and impact
- Handle errors gracefully with try-catch blocks

## Related Documentation
- [VFD Providers DocTypes Documentation](VFD_Providers_DocTypes_Documentation.md)
- [VFD Providers Hooks Documentation](VFD_Providers_Hooks_Documentation.md)
- [VFD Providers Utility Modules Documentation](VFD_Providers_Utility_Modules_Documentation.md)