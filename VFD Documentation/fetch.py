if not frappe.form_dict.start_date:
    frappe.throw(
        "Shift Start Date is mandatory, Please set shift start date to proceed.."
    )
if not frappe.form_dict.end_date:
    frappe.throw("Shift End Date is mandatory, Please set shift end date to proceed..")
if not frappe.form_dict.branch:
    frappe.throw("Branch is mandatory, Please set branch to proceed..")
if not frappe.form_dict.pos_profile:
    frappe.throw("POS Profile is mandatory Please set POS Profile to proceed..")
if not frappe.form_dict.cashier:
    frappe.throw("Cashier is mandatory Please set Cashier to proceed..")
if not frappe.form_dict.previous_cashier:
    frappe.throw(
        "Previous Cashier is mandatory Please set Previous Cashier to proceed.."
    )

# Build the cashier condition
cashier_list = [frappe.form_dict.cashier]
if frappe.form_dict.get("sub_cashier"):
    cashier_list.append(frappe.form_dict.sub_cashier)

query = """
    SELECT 
        si.name AS invoice_id,
        si.posting_date,
        si.posting_time,
        SUM(si.grand_total) AS grand_total,
        SUM(sip.amount) AS amount,
        sip.account,
        sip.mode_of_payment
    FROM `tabSales Invoice` si
    INNER JOIN `tabSales Invoice Payment` sip ON si.name = sip.parent
    WHERE 
        si.docstatus = 1
        AND si.pos_profile = %(pos_profile)s
        AND si.branch = %(branch)s
        AND si.submitted_by IN %(cashier_list)s
        AND TIMESTAMP(posting_date, IFNULL(si.posting_time, '00:00:00')) >= %(start_date)s
        AND TIMESTAMP(posting_date, IFNULL(si.posting_time, '23:59:59')) <= %(end_date)s
        AND sip.type = 'Cash'
    GROUP BY si.name, si.pos_profile, si.branch, sip.mode_of_payment
"""

invoices = frappe.db.sql(
    query,
    {
        "pos_profile": frappe.form_dict.pos_profile,
        "branch": frappe.form_dict.branch,
        "cashier_list": cashier_list,
        "start_date": frappe.form_dict.start_date,
        "end_date": frappe.form_dict.end_date,
    },
    as_dict=True,
)

opening_amount = 0
closing_amount = 0
total_amount = sum([row.amount for row in invoices]) if len(invoices) > 0 else 0

if frappe.form_dict.previous_cashier:
    prev_pos = frappe.db.get_value(
        "POS Shift",
        {
            "cashier": frappe.form_dict.previous_cashier,
            "pos_profile": frappe.form_dict.pos_profile,
            "branch": frappe.form_dict.branch,
        },
        "name",
    )

    if prev_pos:
        prev_pos_doc = frappe.get_doc("POS Shift", prev_pos)
        if len(prev_pos_doc.transaction_data) > 0:
            closing_amount = prev_pos_doc.transaction_data[0].closing_amount

frappe.response.message = {
    "invoices": invoices,
    "total_amount": total_amount,
    "opening_amount": opening_amount,
    "expected_amount": opening_amount + total_amount,
}
