# VFD Providers - DocTypes Documentation

## Overview

This document provides comprehensive information about all DocTypes in the VFD Providers application. DocTypes are the core data structures that define how VFD (Virtual Fiscal Device) information is stored and managed in the system.

## DocTypes Inventory

### Core VFD Provider DocTypes

#### 1. VFD Provider
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/vfd_provider/`
**Module:** VFD Providers
**Purpose:** Defines available VFD service providers and their configurations

##### Fields Structure
| Field Name | Field Type | Purpose | Required |
|------------|------------|---------|----------|
| vfd_provider | Data | Provider name (auto-naming field) | Yes |
| base_url | Data (URL) | API base URL for the provider | No |
| vfd_provider_settings | Link (DocType) | Links to provider-specific settings DocType | Yes |
| vfd_provider_controller | Data | Python controller class path | No |
| attributes | Table (VFD Provider Attribute) | Provider-specific configuration attributes | No |

##### Business Logic
- **Auto-naming:** Uses the `vfd_provider` field as the document name
- **Provider Routing:** Determines which settings DocType to use
- **API Configuration:** Stores base URLs and endpoints
- **Extensibility:** Attributes table allows custom provider configurations

##### Supported Providers
1. **VFDPlus:** Legacy VFD provider
2. **TotalVFD:** Modern VFD provider with enhanced features
3. **SimplifyVFD:** Simplified VFD provider with token authentication

#### 2. VFD Provider Attribute (Child Table)
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/vfd_provider_attribute/`
**Module:** VFD Providers
**Purpose:** Stores key-value pairs for provider-specific configurations

##### Fields Structure
| Field Name | Field Type | Purpose |
|------------|------------|---------|
| key | Data | Configuration parameter name |
| value | Data | Configuration parameter value |

##### Use Cases
- API endpoint definitions
- Provider-specific parameters
- Feature flags and toggles
- Custom configuration options

#### 3. VFD Provider Posting
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/vfd_provider_posting/`
**Module:** VFD Providers
**Purpose:** Records VFD submission attempts and responses

##### Key Features
- **Audit Trail:** Tracks all VFD posting attempts
- **Response Storage:** Stores API responses for debugging
- **Status Tracking:** Monitors success/failure rates
- **Compliance:** Maintains records for tax authority requirements

### VFD Settings DocTypes

#### 1. Company VFD Provider
**Location:** `apps/vfd_providers/vfd_providers/vfd_settings/doctype/company_vfd_provider/`
**Module:** VFD Settings
**Purpose:** Links companies to their designated VFD providers

##### Fields Structure
| Field Name | Field Type | Purpose | Required |
|------------|------------|---------|----------|
| company | Link (Company) | Company reference (auto-naming field) | Yes |
| vfd_provider | Link (VFD Provider) | Selected VFD provider | Yes |

##### Business Logic
- **One-to-One Mapping:** Each company has one VFD provider
- **Auto-naming:** Uses company name as document name
- **Provider Selection:** Determines which VFD service to use

#### 2. SimplifyVFD Settings
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/simplify_vfd_settings/`
**Module:** VFD Providers
**Purpose:** Configuration settings for SimplifyVFD provider

##### Fields Structure
| Field Name | Field Type | Purpose | Required |
|------------|------------|---------|----------|
| company | Link (Company) | Company reference (auto-naming field) | Yes |
| username | Data | SimplifyVFD username | Yes |
| password | Password | SimplifyVFD password | Yes |
| bearer_token | Password | API authentication token | No |
| refresh_token | Password | Token refresh credential | No |
| token_expires | Datetime | Token expiration timestamp | No |

##### Key Features
- **Token Management:** Automatic token refresh every 10 minutes
- **Secure Storage:** Passwords stored using Frappe's encryption
- **Authentication:** Bearer token-based API access
- **Auto-refresh:** Scheduled token renewal to prevent expiration

#### 3. Total VFD Setting
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/total_vfd_setting/`
**Module:** VFD Providers
**Purpose:** Configuration settings for TotalVFD provider

##### Fields Structure
| Field Name | Field Type | Purpose | Required |
|------------|------------|---------|----------|
| company | Link (Company) | Company reference (auto-naming field) | Yes |
| serial_id | Data | VFD device serial identifier | No |
| company_name | Data | Company name (read-only) | No |
| alias_name | Data | Company alias (read-only) | No |
| tin | Data | Tax Identification Number | No |
| vrn | Data | VAT Registration Number | No |
| bearer_token | Password | API authentication token | No |
| x_active_business | Password | Business activation key | No |
| start_date | Date | VFD service start date | No |
| is_vat_grouped | Check | VAT grouping configuration | No |

##### Key Features
- **Device Registration:** Links to physical VFD device
- **Tax Compliance:** Stores TIN and VRN for tax authority
- **Bearer Authentication:** Token-based API access
- **VAT Configuration:** Supports grouped VAT reporting

#### 4. VFDPlus Settings
**Location:** `apps/vfd_providers/vfd_providers/vfd_providers/doctype/vfdplus_settings/`
**Module:** VFD Providers
**Purpose:** Configuration settings for VFDPlus provider (legacy)

##### Fields Structure
| Field Name | Field Type | Purpose |
|------------|------------|---------|
| company | Link (Company) | Company reference (auto-naming field) |
| serial_id | Data | VFD device serial number |
| serial_code | Data | Device serial code |
| account_id | Data | VFDPlus account identifier |
| company_name | Data | Registered company name |
| alias_name | Data | Company alias name |
| env | Select | Environment (sandbox/production) |
| tin | Data | Tax Identification Number |
| vrn | Data | VAT Registration Number |
| serial | Data | Device serial number |
| uin | Data | Unique Identification Number |
| mobile | Data | Contact mobile number |
| address | Data | Company address |
| city | Data | Company city |
| region | Data | Company region |
| street | Data | Street address |
| taxoffice | Data | Tax office designation |
| token_ackmsg | Text | Token acknowledgment message |
| vat_enabled | Check | VAT functionality enabled |
| gc | Int | Global counter |
| dev_type | Data | Device type |
| gov_reg_sdate | Date | Government registration start date |
| gov_reg_edate | Date | Government registration end date |
| sandbox | Check | Sandbox mode enabled |
| vfdplus_api_key | Password | API key for authentication |
| response | Long Text | Last API response |

##### Key Features
- **Legacy Support:** Maintains compatibility with older VFD systems
- **Comprehensive Configuration:** Extensive field set for complex setups
- **Environment Support:** Sandbox and production modes
- **Device Management:** Detailed device registration information

## DocType Relationships

### Entity Relationship Diagram
```
Company
    ↓ (1:1)
Company VFD Provider
    ↓ (N:1)
VFD Provider
    ↓ (1:N)
VFD Provider Attribute

Company
    ↓ (1:1)
[SimplifyVFD Settings | Total VFD Setting | VFDPlus Settings]

Sales Invoice
    ↓ (1:N)
VFD Provider Posting
```

### Key Relationships
1. **Company → Company VFD Provider:** Each company has one VFD provider assignment
2. **Company VFD Provider → VFD Provider:** Multiple companies can use the same provider
3. **VFD Provider → Provider Settings:** Each provider links to its specific settings DocType
4. **Sales Invoice → VFD Provider Posting:** Each VFD submission creates a posting record

## Permissions and Security

### Role-Based Access
- **System Manager:** Full access to all VFD DocTypes
- **Accounts Manager:** Read access to VFD settings and postings
- **Sales User:** Limited access to VFD status information

### Data Security
- **Password Fields:** Encrypted storage for API keys and tokens
- **Audit Trail:** Complete history of VFD submissions
- **Access Control:** Role-based permissions for sensitive data

## Business Workflows

### VFD Provider Setup Workflow
1. **Create VFD Provider:** Define provider with base URL and settings DocType
2. **Configure Provider Settings:** Set up company-specific VFD settings
3. **Assign to Company:** Link company to VFD provider via Company VFD Provider
4. **Test Configuration:** Validate settings with test transactions

### VFD Submission Workflow
1. **Invoice Creation:** Sales Invoice created with VFD-enabled customer
2. **Validation:** Pre-submission validation of VFD requirements
3. **Submission:** Invoice submitted, triggering VFD posting
4. **Provider Routing:** System routes to appropriate VFD provider
5. **API Call:** VFD posting attempt with provider-specific logic
6. **Response Handling:** Update invoice with VFD response data
7. **Audit Trail:** Create VFD Provider Posting record

## Technical Implementation

### DocType Inheritance
- **Standard DocType:** All VFD DocTypes inherit from Frappe's Document class
- **Custom Methods:** Provider-specific logic implemented in Python controllers
- **Client Scripts:** JavaScript enhancements for form behavior

### Data Validation
- **Field Validation:** Built-in Frappe field validation
- **Custom Validation:** Business logic validation in Python
- **Client Validation:** Real-time validation in JavaScript

### Integration Points
- **Hooks Integration:** DocTypes integrated via hooks.py
- **API Integration:** RESTful API calls to VFD providers
- **Event Handling:** Document events for automated processing

## Related Documentation
- [VFD Providers Hooks Documentation](VFD_Providers_Hooks_Documentation.md)
- [VFD Providers Utility Modules Documentation](VFD_Providers_Utility_Modules_Documentation.md)
- [VFD Providers Patch Files Documentation](VFD_Providers_Patch_Files_Documentation.md)