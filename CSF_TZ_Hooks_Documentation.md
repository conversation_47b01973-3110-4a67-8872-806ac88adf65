# CSF TZ - Hooks Configuration Documentation

## Overview

This document provides comprehensive information about the hooks configuration in the CSF TZ application. Hooks are integration points that allow the app to interact with the Frappe framework and extend functionality of existing DocTypes.

## Hooks File Location
**File:** `apps/csf_tz/csf_tz/hooks.py`

## Application Metadata

### Basic Information
```python
app_name = "csf_tz"
app_title = "CSF TZ"
app_publisher = "Aakvatech"
app_description = "Country Specific Functionality Tanzania"
app_icon = "octicon octicon-bookmark"
app_color = "green"
app_email = "<EMAIL>"
app_license = "GNU General Public License (v3)"
required_apps = ["frappe/erpnext", "frappe/hrms"]
```

**Purpose:** Defines the application identity and metadata used by the Frappe framework.

## DocType Overrides

### Class Overrides
```python
override_doctype_class = {
    "Salary Slip": "csf_tz.overrides.salary_slip.SalarySlip",
    "Additional Salary": "csf_tz.overrides.additional_salary.AdditionalSalary",
}
```

**Purpose:** Replaces standard ERPNext classes with CSF TZ customized versions for enhanced functionality.

#### Salary Slip Override
**Class:** `csf_tz.overrides.salary_slip.SalarySlip`
**Enhancements:**
- Custom salary calculation logic
- Tanzania-specific payroll processing
- Enhanced overtime calculations
- Local compliance features

#### Additional Salary Override
**Class:** `csf_tz.overrides.additional_salary.AdditionalSalary`
**Enhancements:**
- Automated additional salary processing
- Integration with piecework calculations
- Custom validation rules

## Client-Side Integrations

### Global Assets
```python
app_include_js = "csf_tz.bundle.js"
app_include_css = "/assets/csf_tz/css/theme.css"
web_include_css = "/assets/csf_tz/css/theme.css"
```

### DocType JavaScript Files
The application includes extensive JavaScript customizations for 25+ DocTypes:

#### Financial Management
| DocType | Script File | Purpose |
|---------|-------------|---------|
| Payment Entry | csf_tz/payment_entry.js | Bank charges, validation enhancements |
| Sales Invoice | csf_tz/sales_invoice.js, authotp/api/sales_invoice.js | Trade-in features, OTP validation |
| Sales Order | csf_tz/sales_order.js | Order processing enhancements |
| Purchase Invoice | csf_tz/purchase_invoice.js | Purchase validation and processing |
| Purchase Order | csf_tz/purchase_order.js | Purchase order enhancements |
| Purchase Receipt | csf_tz/purchase_receipt.js | Receipt processing |
| Quotation | csf_tz/quotation.js | Quotation enhancements |

#### Inventory Management
| DocType | Script File | Purpose |
|---------|-------------|---------|
| Stock Entry | csf_tz/stock_entry.js | Stock movement enhancements |
| Stock Reconciliation | csf_tz/stock_reconciliation.js | Reconciliation improvements |
| Delivery Note | csf_tz/delivery_note.js | Delivery processing |
| Landed Cost Voucher | csf_tz/landed_cost_voucher.js | Cost allocation |
| BOM | csf_tz/bom_addittional_costs.js | Additional cost management |

#### Human Resources
| DocType | Script File | Purpose |
|---------|-------------|---------|
| Payroll Entry | csf_tz/payroll_entry.js, stanbic/payroll_entry.js | Payroll processing, bank integration |
| Salary Slip | csf_tz/salary_slip.js | Salary calculation enhancements |
| Additional Salary | csf_tz/additional_salary.js | Additional salary management |
| Employee Advance | csf_tz/employee_advance.js | Advance processing |
| Employee | csf_tz/employee_contact_qr.js | QR code generation |
| Travel Request | csf_tz/travel_request.js | Travel management |

#### Customer and Supplier Management
| DocType | Script File | Purpose |
|---------|-------------|---------|
| Customer | csf_tz/customer.js, authotp/api/customer.js | Customer enhancements, OTP |
| Supplier | csf_tz/supplier.js | Supplier management |

#### System Management
| DocType | Script File | Purpose |
|---------|-------------|---------|
| Account | csf_tz/account.js | Account management |
| Company | csf_tz/company.js | Company configuration |
| Warehouse | csf_tz/warehouse.js | Warehouse management |
| Bank Reconciliation | csf_tz/bank_reconciliation.js | Bank reconciliation |

#### Education Management
| DocType | Script File | Purpose |
|---------|-------------|---------|
| Fees | csf_tz/fees.js | Fee management |
| Program Enrollment Tool | csf_tz/program_enrollment_tool.js | Enrollment processing |
| Program Enrollment | csf_tz/program_enrollment.js | Enrollment management |
| Student Applicant | csf_tz/student_applicant.js | Applicant processing |

### List View Customizations
```python
doctype_list_js = {
    "Custom Field": "csf_tz/custom_field.js",
    "Property Setter": "csf_tz/property_setter.js",
}
```

## Installation and Migration Hooks

### After Install Hooks
```python
after_install = [
    "csf_tz.patches.custom_fields.custom_fields_for_removed_edu_fields_in_csf_tz.execute",
    "csf_tz.patches.remove_stock_entry_qty_field.execute",
    "csf_tz.patches.remove_core_doctype_custom_docperm.execute",
    "csf_tz.patches.add_custom_fields_for_sales_invoice_item_and_purchase_invoice_item.execute",
    "csf_tz.patches.add_custom_fields_on_customer_for_auto_close_dn.execute",
    "csf_tz.patches.custom_fields.create_custom_fields_for_additional_salary.execute",
    "csf_tz.patches.custom_fields.auth_otp_custom_fields.execute",
    "csf_tz.patches.custom_fields.payroll_approval_custom_fields.execute",
    "csf_tz.utils.create_custom_fields.execute",
    "csf_tz.utils.create_property_setter.execute",
]
```

**Purpose:** Executes essential setup tasks immediately after app installation.

### After Migration Hooks
```python
after_migrate = [
    "csf_tz.utils.create_custom_fields.execute",
    "csf_tz.utils.create_property_setter.execute",
    "csf_tz.patches.update_payware_settings_values_to_csf_tz_settings.execute",
    "csf_tz.patches.custom_fields.create_custom_fields_for_trade_in_feature.execute",
]
```

**Purpose:** Ensures customizations are applied after database migrations.

## Document Events Configuration

### Sales Invoice Events
```python
"Sales Invoice": {
    "before_submit": [
        "csf_tz.custom_api.validate_grand_total",
        "csf_tz.authotp.api.sales_invoice.before_submit",
    ],
    "on_submit": [
        "csf_tz.custom_api.validate_net_rate",
        "csf_tz.custom_api.create_delivery_note",
        "csf_tz.custom_api.check_submit_delivery_note",
        "csf_tz.custom_api.make_withholding_tax_gl_entries_for_sales",
        "csf_tz.custom_api.create_trade_in_stock_entry",
    ],
    "validate": [
        "csf_tz.custom_api.check_validate_delivery_note",
        "csf_tz.custom_api.validate_items_remaining_qty",
        "csf_tz.custom_api.calculate_price_reduction",
        "csf_tz.custom_api.validate_trade_in_serial_no_and_batch",
        "csf_tz.custom_api.validate_trade_in_sales_percentage",
    ],
    "before_cancel": "csf_tz.custom_api.check_cancel_delivery_note",
    "before_insert": "csf_tz.custom_api.batch_splitting",
}
```

**Key Features:**
- **Automatic Delivery Note Creation:** Creates delivery notes automatically on invoice submission
- **Trade-in Processing:** Handles trade-in transactions and stock entries
- **Withholding Tax:** Automatic withholding tax GL entries
- **OTP Validation:** Security validation before submission
- **Batch Splitting:** Automatic batch splitting for inventory items

### Payroll and HR Events

#### Salary Slip Processing
```python
"Salary Slip": {
    "before_insert": [
        "csf_tz.csftz_hooks.payroll.before_insert_salary_slip",
        "csf_tz.csftz_hooks.payroll.generate_component_in_salary_slip_insert",
    ],
    "before_save": "csf_tz.csftz_hooks.payroll.generate_component_in_salary_slip_update",
}
```

#### Payroll Entry Management
```python
"Payroll Entry": {
    "before_insert": "csf_tz.csftz_hooks.payroll.before_insert_payroll_entry",
    "before_update_after_submit": "csf_tz.csftz_hooks.payroll.before_update_after_submit",
    "before_cancel": "csf_tz.csftz_hooks.payroll.before_cancel_payroll_entry",
}
```

#### Additional Salary Processing
```python
"Additional Salary": {
    "on_submit": "csf_tz.csftz_hooks.additional_salary.create_additional_salary_journal",
    "before_validate": "csf_tz.csftz_hooks.additional_salary.set_employee_base_salary_in_hours",
}
```

#### Employee Advance Management
```python
"Employee Advance": {
    "on_submit": "csf_tz.csftz_hooks.employee_advance_payment_and_expense.execute",
}
```

#### Attendance and Time Tracking
```python
"Attendance": {
    "validate": "csf_tz.csftz_hooks.attendance.process_overtime",
}
"Employee Checkin": {
    "validate": "csf_tz.csftz_hooks.employee_checkin.validate",
}
```

### Financial Management Events

#### Payment Entry Processing
```python
"Payment Entry": {
    "validate": "csf_tz.csftz_hooks.payment_entry.validate",
    "before_submit": [
        "csf_tz.csftz_hooks.bank_charges_payment_entry.validate_bank_charges_account",
        "csf_tz.csftz_hooks.bank_charges_payment_entry.create_bank_charges_journal",
    ],
}
```

#### Purchase Invoice Events
```python
"Purchase Invoice": {
    "on_submit": "csf_tz.custom_api.make_withholding_tax_gl_entries_for_purchase",
}
```

#### Purchase Order Events
```python
"Purchase Order": {
    "validate": "csf_tz.custom_api.target_warehouse_based_price_list",
}
```

### Inventory Management Events

#### Stock Entry Processing
```python
"Stock Entry": {
    "validate": "csf_tz.custom_api.calculate_total_net_weight",
    "before_save": "csf_tz.csftz_hooks.stock.import_from_bom",
}
```

#### Delivery Note Integration
```python
"Delivery Note": {
    "on_submit": "csf_tz.custom_api.update_delivery_on_sales_invoice",
    "before_cancel": "csf_tz.custom_api.update_delivery_on_sales_invoice",
}
```

#### Landed Cost Management
```python
"Landed Cost Voucher": {
    "validate": [
        "csf_tz.csftz_hooks.landed_cost_voucher.total_amount",
    ]
}
```

### Education Management Events

#### Fee Processing
```python
"Fees": {
    "before_insert": "csf_tz.custom_api.set_fee_abbr",
    "after_insert": "csf_tz.bank_api.set_callback_token",
    "on_submit": "csf_tz.bank_api.invoice_submission",
    "before_cancel": "csf_tz.custom_api.on_cancel_fees",
}
```

#### Program Enrollment
```python
"Program Enrollment": {
    "onload": "csf_tz.csftz_hooks.program_enrollment.create_course_enrollments_override",
    "refresh": "csf_tz.csftz_hooks.program_enrollment.create_course_enrollments_override",
    "reload": "csf_tz.csftz_hooks.program_enrollment.create_course_enrollments_override",
    "before_submit": "csf_tz.csftz_hooks.program_enrollment.validate_submit_program_enrollment",
}
```

#### Student Applicant Processing
```python
"Student Applicant": {
    "on_update_after_submit": "csf_tz.csftz_hooks.student_applicant.make_student_applicant_fees",
}
```

### System-Wide Events

#### Universal Visibility System
```python
"*": {
    "validate": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "onload": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "before_insert": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "after_insert": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "before_naming": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "before_change": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "before_update_after_submit": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "before_validate": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "before_save": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "on_update": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "before_submit": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "autoname": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "on_cancel": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "on_trash": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "on_submit": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "on_update_after_submit": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
    "on_change": ["csf_tz.csf_tz.doctype.visibility.visibility.run_visibility"],
}
```

**Purpose:** Implements a universal visibility system that controls field visibility and behavior across all DocTypes based on configurable rules.

## Scheduled Tasks Configuration

### Cron Jobs
```python
"cron": {
    "0 */6 * * *": [  # Every 6 hours
        "csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles",
    ],
    "0 */2 * * *": [  # Every 2 hours
        "csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles",
    ],
    "*/15 * * * *": [  # Every 15 minutes
        "csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty",
        "csf_tz.stanbic.sftp.sync_all_stanbank_files",
        "csf_tz.stanbic.sftp.process_download_files",
    ],
    "30 3 * * *": [  # Daily at 3:30 AM
        "csf_tz.custom_api.auto_close_dn",
    ],
    "40 3 * * *": [  # Daily at 3:40 AM
        "csf_tz.csftz_hooks.material_request.auto_close_material_request",
    ],
}
```

#### Vehicle Management Tasks
- **Parking Bill Monitoring:** Checks parking bills for all vehicles every 6 hours
- **Fine Record Monitoring:** Monitors vehicle fines every 2 hours

#### Financial Integration Tasks
- **Bank File Synchronization:** Syncs Stanbic bank files every 15 minutes
- **Inventory Revaluation:** Processes incorrect balance quantities every 15 minutes

#### Daily Maintenance Tasks
- **Delivery Note Auto-Close:** Automatically closes delivery notes at 3:30 AM
- **Material Request Auto-Close:** Closes material requests at 3:40 AM

### Daily Tasks
```python
"daily": [
    "csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice",
    "csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts",
    "csf_tz.bank_api.reconciliation",
    "csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records",
]
```

**Daily Operations:**
- **Delivery Note Creation:** Creates delivery notes for pending sales invoices
- **Visibility Alerts:** Triggers daily alert notifications
- **Bank Reconciliation:** Performs daily bank reconciliation
- **Additional Salary Generation:** Generates additional salary records

### Weekly Tasks
```python
"weekly": [
    "csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request"
]
```

**Weekly Operations:**
- **Stock Reconciliation:** Creates stock reconciliation for pending material requests

### Monthly Tasks
```python
"monthly": [
    "csf_tz.csf_tz.doctype.tz_insurance_cover_note.tz_insurance_cover_note.update_covernote_docs"
]
```

**Monthly Operations:**
- **Insurance Cover Note Updates:** Updates insurance cover note documents

## Method Overrides

### Whitelisted Method Overrides
```python
override_whitelisted_methods = {
    "frappe.desk.query_report.get_script": "csf_tz.csftz_hooks.query_report.get_script",
    "erpnext.buying.doctype.purchase_order.purchase_order.update_status": "csf_tz.csftz_hooks.purchase_order.update_po_status",
    "erpnext.buying.doctype.purchase_order.purchase_order.close_or_unclose_purchase_orders": "csf_tz.csftz_hooks.purchase_order.close_or_unclose_purchase_orders",
    "erpnext.stock.doctype.material_request.material_request.update_status": "csf_tz.csftz_hooks.material_request.update_mr_status",
    "erpnext.stock.get_item_details.get_item_details": "csf_tz.csftz_hooks.custom_get_item_details.custom_get_item_details",
}
```

#### Query Report Enhancement
**Override:** `frappe.desk.query_report.get_script`
**Purpose:** Enhances query report functionality with CSF TZ-specific features

#### Purchase Order Management
**Overrides:** Purchase order status update methods
**Purpose:** Custom purchase order status management and closing logic

#### Material Request Processing
**Override:** Material request status update
**Purpose:** Enhanced material request status management

#### Item Details Enhancement
**Override:** `get_item_details`
**Purpose:** Custom item details retrieval with CSF TZ-specific logic

## Jinja Integration

### Template Methods
```python
jinja = {"methods": ["csf_tz.custom_api.generate_qrcode"]}
```

**Purpose:** Makes QR code generation available in Jinja templates for print formats and reports.

## Business Process Integration

### Sales and Delivery Workflow
1. **Sales Invoice Creation:** Validates grand total and items
2. **Automatic Delivery Note:** Creates delivery note on invoice submission
3. **Trade-in Processing:** Handles trade-in stock entries
4. **Withholding Tax:** Automatic tax calculations and GL entries

### Payroll Processing Workflow
1. **Salary Slip Generation:** Custom component generation and calculations
2. **Additional Salary:** Automated additional salary processing
3. **Employee Advance:** Automatic payment and expense entries
4. **Overtime Processing:** Attendance-based overtime calculations

### Inventory Management Workflow
1. **Stock Entry Validation:** Weight calculations and BOM imports
2. **Material Request Processing:** Auto-closure and status management
3. **Stock Reconciliation:** Weekly reconciliation for pending requests
4. **Inventory Revaluation:** Regular balance quantity corrections

### Financial Integration Workflow
1. **Payment Entry Processing:** Bank charges and validation
2. **Bank Reconciliation:** Daily automated reconciliation
3. **Withholding Tax:** Automatic GL entries for sales and purchases
4. **Landed Cost Management:** Enhanced cost allocation

## Security and Compliance

### OTP Authentication
- **Sales Invoice Security:** OTP validation before submission
- **Customer Management:** OTP integration for customer operations

### Audit and Visibility
- **Universal Visibility System:** Controls field visibility across all DocTypes
- **Change Tracking:** Comprehensive audit trails
- **Permission Management:** Custom DocPerm validation

### Data Integrity
- **Validation Hooks:** Extensive validation across all business processes
- **Auto-correction:** Scheduled tasks for data consistency
- **Error Prevention:** Pre-submission validations

## Performance Optimization

### Scheduled Processing
- **Background Tasks:** Heavy operations moved to scheduled tasks
- **Batch Processing:** Bulk operations for efficiency
- **Resource Management:** Optimized task scheduling

### Caching and Efficiency
- **Method Overrides:** Optimized core functionality
- **Custom Queries:** Enhanced database operations
- **Reduced Overhead:** Streamlined processing workflows

## Related Documentation
- [CSF TZ DocTypes Documentation](CSF_TZ_DocTypes_Documentation.md)
- [CSF TZ Custom Fields Documentation](CSF_TZ_Custom_Fields_Documentation.md)
- [CSF TZ Property Setters Documentation](CSF_TZ_Property_Setters_Documentation.md)
- [CSF TZ Independent Scripts Documentation](CSF_TZ_Independent_Scripts_Documentation.md)